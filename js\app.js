document.addEventListener('DOMContentLoaded', function() {
  // Vérifier l'authentification de manière plus robuste
  console.log('🔐 Vérification de l\'authentification...');
  console.log('URL actuelle:', window.location.href);
  console.log('Pathname:', window.location.pathname);

  const token = localStorage.getItem('authToken');
  const isLoginPage = window.location.href.includes('login.html') || window.location.pathname.includes('login.html');

  console.log('Token présent:', !!token);
  console.log('Page de connexion:', isLoginPage);

  if (!token && !isLoginPage) {
    console.log('❌ Pas de token et pas sur la page de connexion - Redirection...');
    window.location.href = 'login.html';
    return;
  }

  if (token && isLoginPage) {
    console.log('✅ Token présent sur la page de connexion - Redirection vers l\'app...');
    window.location.href = 'index.html';
    return;
  }

  console.log('✅ Authentification OK, chargement de l\'application...');

  // Gestionnaire de navigation
  const navLinks = document.querySelectorAll('[data-section]');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const section = this.getAttribute('data-section');
      loadSection(section);

      // Marquer le lien comme actif
      document.querySelectorAll('[data-section]').forEach(l => l.classList.remove('active'));
      this.classList.add('active');

      // Ouvrir automatiquement le menu parent si nécessaire
      const parentCollapse = this.closest('.collapse');
      if (parentCollapse && !parentCollapse.classList.contains('show')) {
        const collapseInstance = new bootstrap.Collapse(parentCollapse, { show: true });
      }
    });
  });

  // Gestionnaire pour les menus déroulants
  document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(toggle => {
    toggle.addEventListener('click', function(e) {
      e.preventDefault();

      const targetId = this.getAttribute('data-bs-target');
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        const collapseInstance = new bootstrap.Collapse(targetElement, { toggle: true });
      }
    });
  });

  // Gestionnaire de déconnexion (navbar)
  const navbarLogout = document.getElementById('navbarLogout');
  if (navbarLogout) {
    navbarLogout.addEventListener('click', function(e) {
      e.preventDefault();
      auth.logout();
    });
  }

  // Gestionnaire du bouton de thème dans la navbar
  const navbarThemeToggle = document.getElementById('navbarThemeToggle');
  if (navbarThemeToggle) {
    navbarThemeToggle.addEventListener('click', function(e) {
      e.preventDefault();
      if (window.themeManager) {
        window.themeManager.toggleTheme();
        updateNavbarThemeButton();
      }
    });
  }

  // Mettre à jour le bouton de thème au chargement
  setTimeout(updateNavbarThemeButton, 100);

  // Afficher le nom d'utilisateur
  const usernameDisplay = document.getElementById('username-display');
  if (usernameDisplay && auth.getUsername) {
    usernameDisplay.textContent = auth.getUsername();
  }

  // Charger la section par défaut
  loadSection('dashboard');

  // Fonction pour ouvrir automatiquement le menu parent d'une section
  function openParentMenu(section) {
    const purchaseSections = ['purchase-orders', 'purchase-receipts', 'purchase-invoices'];
    const salesSections = ['quotes', 'delivery', 'invoices'];
    const returnsSections = ['sales-returns', 'purchase-returns'];

    if (purchaseSections.includes(section)) {
      const purchasesCollapse = document.getElementById('purchasesCollapse');
      if (purchasesCollapse && !purchasesCollapse.classList.contains('show')) {
        const collapseInstance = new bootstrap.Collapse(purchasesCollapse, { show: true });
      }
    } else if (salesSections.includes(section)) {
      const salesCollapse = document.getElementById('salesCollapse');
      if (salesCollapse && !salesCollapse.classList.contains('show')) {
        const collapseInstance = new bootstrap.Collapse(salesCollapse, { show: true });
      }
    } else if (returnsSections.includes(section)) {
      const returnsCollapse = document.getElementById('returnsCollapse');
      if (returnsCollapse && !returnsCollapse.classList.contains('show')) {
        const collapseInstance = new bootstrap.Collapse(returnsCollapse, { show: true });
      }
    }
  }

  // Fonction pour charger une section
  function loadSection(section) {
    const sectionTitle = document.getElementById('section-title');
    const container = document.getElementById('app-container');

    // Ouvrir automatiquement le menu parent si nécessaire
    openParentMenu(section);

    // Mettre à jour le titre
    if (sectionTitle) {
      sectionTitle.textContent = getSectionTitle(section);
    }

    // Charger le contenu
    if (container) {
      container.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

      fetch(`sections/${section}.html`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }
          return response.text();
        })
        .then(html => {
          container.innerHTML = html;

          // Appliquer le thème aux nouveaux éléments
          if (window.applyThemeToNewElements) {
            window.applyThemeToNewElements(container);
          }

          // Corriger les couleurs selon le thème
          if (window.fixTextColors) {
            setTimeout(window.fixTextColors, 100);
          }

          initSection(section);
        })
        .catch(error => {
          container.innerHTML = '<div class="alert alert-danger">Erreur de chargement du contenu: ' + error.message + '</div>';
          console.error('Erreur:', error);
        });
    } else {
      console.error('Élément container non trouvé');
    }
  }

  // Fonction pour initialiser une section spécifique
  function initSection(section) {
    // Déclencher l'événement de changement de section
    const sectionChangedEvent = new CustomEvent('sectionChanged', {
      detail: { section: section }
    });
    document.dispatchEvent(sectionChangedEvent);

    try {
      switch(section) {
        case 'dashboard':
          initDashboardSection();
          break;
        case 'products':
          initProductsSection();
          break;
        case 'clients':
          initClientsSection();
          break;
        case 'suppliers':
          initSuppliersSection();
          break;
        case 'pos':
          initPOSSection();
          break;
        case 'quotes':
          initQuotesSection();
          break;
        case 'purchase-orders':
          initPurchaseOrdersSection();
          break;
        case 'purchase-receipts':
          initPurchaseReceiptsSection();
          break;
        case 'purchase-invoices':
          initPurchaseInvoicesSection();
          break;
        case 'invoices':
          initInvoicesSection();
          break;
        case 'delivery':
          initDeliverySection();
          break;
        case 'inventory':
          initInventorySection();
          break;
        case 'sales-returns':
          initSalesReturnsSection();
          break;
        case 'purchase-returns':
          initPurchaseReturnsSection();
          break;
        case 'settings':
          initSettingsSection();
          break;
        case 'super-admin':
          initSuperAdminSection();
          break;
        default:
          console.log('Section non reconnue:', section);
      }
    } catch (error) {
      console.error(`Erreur lors de l'initialisation de la section ${section}:`, error);
      document.getElementById('app-container').innerHTML =
        `<div class="alert alert-danger">Erreur lors de l'initialisation de la section: ${error.message}</div>`;
    }
  }

  // Fonction pour obtenir le titre d'une section
  function getSectionTitle(section) {
    const titles = {
      'dashboard': 'Tableau de bord',
      'products': 'Produits',
      'clients': 'Clients',
      'suppliers': 'Fournisseurs',
      'pos': 'Caisse',
      'quotes': 'Devis',
      'purchase-orders': 'Bons de commande',
      'purchase-receipts': 'Bons de réception',
      'purchase-invoices': 'Factures d\'achat',
      'invoices': 'Factures de vente',
      'delivery': 'Bons de livraison',
      'inventory': 'Inventaire',
      'sales-returns': 'Retours clients',
      'purchase-returns': 'Retours fournisseurs',
      'settings': 'Paramètres',
      'super-admin': 'Super Administration'
    };

    return titles[section] || 'Section inconnue';
  }

  // Ajouter cette fonction pour le tableau de bord
  function initDashboardSection() {
    // Initialiser le tableau de bord
    loadDashboardData();
  }

  // Fonction pour initialiser la section bons de commande
  function initPurchaseOrdersSection() {
    console.log('📄 Initialisation de la section bons de commande...');
    // Le gestionnaire sera initialisé par l'événement sectionChanged
    if (window.initPurchaseOrdersManager) {
      window.initPurchaseOrdersManager();
    }
  }

  // Fonction pour charger les données du tableau de bord
  async function loadDashboardData() {
    try {
      // Récupérer les produits pour les statistiques de stock
      const products = await productManager.getAll();

      // Calculer les statistiques
      const totalProducts = products.length;
      const totalStock = products.reduce((sum, product) => sum + product.stockQuantity, 0);
      const lowStockProducts = products.filter(product => product.stockQuantity < 10).length;
      const outOfStockProducts = products.filter(product => product.stockQuantity === 0).length;

      // Calculer la valeur totale du stock
      const stockValue = products.reduce((sum, product) => sum + (product.price * product.stockQuantity), 0);

      // Mettre à jour les statistiques dans le DOM
      document.getElementById('total-products').textContent = totalProducts;
      document.getElementById('total-stock').textContent = totalStock;
      document.getElementById('low-stock').textContent = lowStockProducts;
      document.getElementById('out-of-stock').textContent = outOfStockProducts;
      document.getElementById('stock-value').textContent = stockValue.toFixed(2) + ' DA';

      // Générer la liste des produits en rupture de stock
      const lowStockList = document.getElementById('low-stock-list');
      lowStockList.innerHTML = '';

      const lowStockItems = products.filter(product => product.stockQuantity < 10);
      if (lowStockItems.length === 0) {
        lowStockList.innerHTML = '<li class="list-group-item">Aucun produit en stock faible</li>';
      } else {
        lowStockItems.forEach(product => {
          lowStockList.innerHTML += `
            <li class="list-group-item d-flex justify-content-between align-items-center">
              ${product.name}
              <span class="badge bg-warning rounded-pill">${product.stockQuantity}</span>
            </li>
          `;
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données du tableau de bord:', error);
      document.getElementById('dashboard-container').innerHTML =
        '<div class="alert alert-danger">Erreur lors du chargement des données du tableau de bord</div>';
    }
  }

  // Fonction pour mettre à jour le bouton de thème de la navbar
  function updateNavbarThemeButton() {
    if (!window.themeManager) return;

    const currentTheme = window.themeManager.getCurrentTheme();

    // Mettre à jour le bouton de la navbar
    const navbarButton = document.getElementById('navbarThemeToggle');
    if (navbarButton) {
      const icon = navbarButton.querySelector('i');
      if (currentTheme === 'dark') {
        icon.className = 'bi bi-sun-fill';
        navbarButton.title = 'Passer au mode clair';
      } else {
        icon.className = 'bi bi-moon-fill';
        navbarButton.title = 'Passer au mode sombre';
      }
    }
  }

  // Rendre la fonction globale pour qu'elle soit accessible
  window.updateNavbarThemeButton = updateNavbarThemeButton;
});










