<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Confirm</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
  <div class="container mt-5">
    <h1>Test de la fonction showConfirm</h1>
    <button id="testBtn" class="btn btn-danger">Tester la suppression</button>
    <div id="result" class="mt-3"></div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="js/notifications.js"></script>
  <script>
    document.getElementById('testBtn').addEventListener('click', async () => {
      const result = document.getElementById('result');
      
      try {
        console.log('Test de showConfirm...');
        
        if (typeof window.showConfirm === 'function') {
          result.innerHTML = '<p class="text-info">Fonction showConfirm trouvée, test en cours...</p>';
          
          const confirmed = await window.showConfirm(
            'Voulez-vous vraiment supprimer cet élément ?',
            'Confirmation de suppression',
            {
              confirmText: 'Supprimer',
              cancelText: 'Annuler'
            }
          );
          
          if (confirmed) {
            result.innerHTML = '<p class="text-success">✅ Suppression confirmée !</p>';
          } else {
            result.innerHTML = '<p class="text-warning">❌ Suppression annulée</p>';
          }
        } else {
          result.innerHTML = '<p class="text-danger">❌ Fonction showConfirm non trouvée</p>';
          
          // Test avec confirm() classique
          const confirmed = confirm('Voulez-vous vraiment supprimer cet élément ?');
          if (confirmed) {
            result.innerHTML += '<p class="text-success">✅ Confirm() classique fonctionne</p>';
          } else {
            result.innerHTML += '<p class="text-warning">❌ Confirm() classique annulé</p>';
          }
        }
      } catch (error) {
        console.error('Erreur:', error);
        result.innerHTML = '<p class="text-danger">❌ Erreur: ' + error.message + '</p>';
      }
    });
  </script>
</body>
</html>
