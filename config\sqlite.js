// Configuration de la base de données SQLite (alternative à MySQL)
console.log('📦 Chargement du module SQLite...');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
console.log('✅ Module SQLite chargé avec succès');

// Chemin vers la base de données SQLite
const dbPath = path.join(__dirname, '..', 'database', 'gesticom.db');

// Créer une connexion à la base de données
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Erreur de connexion à SQLite:', err.message);
  } else {
    console.log('✅ Connexion à la base de données SQLite réussie');
  }
});

// Fonction pour tester la connexion
async function testConnection() {
  return new Promise((resolve, reject) => {
    db.get("SELECT 1", (err) => {
      if (err) {
        console.error('❌ Erreur de connexion à la base de données:', err.message);
        resolve(false);
      } else {
        console.log('✅ Connexion à la base de données SQLite réussie');
        resolve(true);
      }
    });
  });
}

// Fonction pour exécuter une requête
async function query(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        console.error('Erreur lors de l\'exécution de la requête:', err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// Fonction pour obtenir une seule ligne
async function queryOne(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        console.error('Erreur lors de l\'exécution de la requête:', err);
        reject(err);
      } else {
        resolve(row || null);
      }
    });
  });
}

// Fonction pour exécuter une requête d'insertion/mise à jour
async function run(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        console.error('Erreur lors de l\'exécution de la requête:', err);
        reject(err);
      } else {
        resolve({ insertId: this.lastID, changes: this.changes });
      }
    });
  });
}

// Initialiser les tables si elles n'existent pas
async function initializeTables() {
  try {
    console.log('🔧 Initialisation des tables SQLite...');

    // Table des catégories
    await run(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des produits
    await run(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        stock_quantity INTEGER NOT NULL DEFAULT 0,
        category_id INTEGER,
        barcode TEXT,
        sku TEXT,
        min_stock_level INTEGER DEFAULT 5,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )
    `);

    // Table des clients
    await run(`
      CREATE TABLE IF NOT EXISTS clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        contact_person TEXT,
        address TEXT,
        city TEXT,
        postal_code TEXT,
        country TEXT DEFAULT 'Algérie',
        phone TEXT,
        email TEXT,
        tax_id TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des fournisseurs
    await run(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        contact_person TEXT,
        address TEXT,
        city TEXT,
        postal_code TEXT,
        country TEXT DEFAULT 'Algérie',
        phone TEXT,
        email TEXT,
        tax_id TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des factures
    await run(`
      CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        client_id INTEGER NOT NULL,
        date DATE NOT NULL,
        due_date DATE,
        subtotal REAL NOT NULL,
        tax_rate REAL DEFAULT 19.00,
        tax_amount REAL NOT NULL,
        total REAL NOT NULL,
        amount_paid REAL DEFAULT 0,
        status TEXT DEFAULT 'unpaid',
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (client_id) REFERENCES clients(id)
      )
    `);

    // Table des détails de factures
    await run(`
      CREATE TABLE IF NOT EXISTS invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        discount_percent REAL DEFAULT 0,
        tax_percent REAL DEFAULT 19.00,
        total REAL NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id)
      )
    `);

    console.log('✅ Tables SQLite initialisées avec succès');

    // Insérer des données de test si les tables sont vides
    await insertTestData();

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation des tables:', error);
  }
}

// Fonction pour insérer des données de test
async function insertTestData() {
  try {
    // Vérifier si des données existent déjà
    const existingCategories = await query('SELECT COUNT(*) as count FROM categories');

    if (existingCategories[0].count === 0) {
      console.log('📦 Insertion des données de test...');

      // Insérer les catégories
      await run("INSERT INTO categories (name, description) VALUES (?, ?)", ['Informatique', 'Produits informatiques et accessoires']);
      await run("INSERT INTO categories (name, description) VALUES (?, ?)", ['Téléphonie', 'Téléphones mobiles et accessoires']);
      await run("INSERT INTO categories (name, description) VALUES (?, ?)", ['Bureautique', 'Fournitures et équipements de bureau']);
      await run("INSERT INTO categories (name, description) VALUES (?, ?)", ['Accessoires', 'Accessoires divers']);

      // Insérer les produits
      await run("INSERT INTO products (name, description, price, stock_quantity, category_id, barcode, sku, min_stock_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        ['Ordinateur portable', 'Ordinateur portable 15.6" Core i5', 89999.99, 15, 1, '123456789', 'ORD-001', 3]);
      await run("INSERT INTO products (name, description, price, stock_quantity, category_id, barcode, sku, min_stock_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        ['Smartphone', 'Smartphone Android 6.5"', 49999.99, 25, 2, '987654321', 'TEL-001', 5]);
      await run("INSERT INTO products (name, description, price, stock_quantity, category_id, barcode, sku, min_stock_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        ['Écran 24 pouces', 'Écran LED Full HD 24"', 19999.99, 10, 1, '456789123', 'ECR-001', 2]);
      await run("INSERT INTO products (name, description, price, stock_quantity, category_id, barcode, sku, min_stock_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        ['Souris sans fil', 'Souris optique sans fil', 2999.99, 50, 4, '789123456', 'SOU-001', 10]);

      // Insérer les clients
      await run("INSERT INTO clients (name, contact_person, address, city, phone, email) VALUES (?, ?, ?, ?, ?, ?)",
        ['Entreprise ABC', 'Jean Dupont', '123 Rue du Commerce', 'Alger', '021 23 45 67', '<EMAIL>']);
      await run("INSERT INTO clients (name, contact_person, address, city, phone, email) VALUES (?, ?, ?, ?, ?, ?)",
        ['Société XYZ', 'Marie Martin', '456 Avenue de la Liberté', 'Oran', '041 98 76 54', '<EMAIL>']);
      await run("INSERT INTO clients (name, contact_person, address, city, phone, email) VALUES (?, ?, ?, ?, ?, ?)",
        ['Client Particulier', 'Ahmed Benali', '789 Boulevard des Martyrs', 'Constantine', '031 45 67 89', '<EMAIL>']);

      // Insérer les fournisseurs
      await run("INSERT INTO suppliers (name, contact_person, address, city, phone, email) VALUES (?, ?, ?, ?, ?, ?)",
        ['Tech Supplies Inc.', 'John Smith', '123 Tech Street', 'Alger', '021 11 22 33', '<EMAIL>']);
      await run("INSERT INTO suppliers (name, contact_person, address, city, phone, email) VALUES (?, ?, ?, ?, ?, ?)",
        ['Global Electronics', 'Sarah Johnson', '45 Electronics Avenue', 'Oran', '041 44 55 66', '<EMAIL>']);
      await run("INSERT INTO suppliers (name, contact_person, address, city, phone, email) VALUES (?, ?, ?, ?, ?, ?)",
        ['Office Solutions', 'Mike Wilson', '78 Office Boulevard', 'Constantine', '031 77 88 99', '<EMAIL>']);

      console.log('✅ Données de test insérées avec succès');
    }
  } catch (error) {
    console.error('❌ Erreur lors de l\'insertion des données de test:', error);
  }
}

module.exports = {
  db,
  query,
  queryOne,
  run,
  testConnection,
  initializeTables
};
