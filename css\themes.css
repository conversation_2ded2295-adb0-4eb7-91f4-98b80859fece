/* ===== SYSTÈME DE THÈMES ===== */

/* Variables CSS pour le thème clair (par défaut) */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #6c757d;
  --border-color: #dee2e6;
  --sidebar-bg: #f8f9fa;
  --sidebar-text: #212529;
  --sidebar-hover: #e9ecef;
  --card-bg: #ffffff;
  --card-border: #dee2e6;
  --input-bg: #ffffff;
  --input-border: #ced4da;
  --modal-bg: #ffffff;
  --table-bg: #ffffff;
  --table-stripe: #f8f9fa;
  --table-hover: #f5f5f5;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.15);
}

/* Variables CSS pour le thème sombre */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3d3d3d;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #888888;
  --border-color: #404040;
  --sidebar-bg: #2d2d2d;
  --sidebar-text: #ffffff;
  --sidebar-hover: #3d3d3d;
  --card-bg: #2d2d2d;
  --card-border: #404040;
  --input-bg: #3d3d3d;
  --input-border: #555555;
  --modal-bg: #2d2d2d;
  --table-bg: #2d2d2d;
  --table-stripe: #3d3d3d;
  --table-hover: #404040;
  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-lg: rgba(0, 0, 0, 0.5);
}

/* RÈGLE GLOBALE POUR FORCER LE TEXTE BLANC EN MODE SOMBRE UNIQUEMENT */
[data-theme="dark"] *:not(.btn):not(.badge):not(.alert) {
  color: #ffffff !important;
}

/* Amélioration du mode sombre */
html[data-theme="dark"] {
  background-color: #1a1a1a;
  color: #ffffff;
}

html[data-theme="dark"] body {
  background-color: #1a1a1a;
  color: #ffffff;
}

/* Exceptions pour les éléments qui ont besoin de couleurs spécifiques en mode sombre */
[data-theme="dark"] .text-muted {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .text-secondary {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .btn-warning,
[data-theme="dark"] .btn-info,
[data-theme="dark"] .btn-light {
  color: #000000 !important;
}

[data-theme="dark"] .badge.bg-warning,
[data-theme="dark"] .badge.bg-info,
[data-theme="dark"] .badge.bg-light {
  color: #000000 !important;
}

/* ===== STYLES GÉNÉRAUX ===== */

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* RÈGLES POUR LE MODE CLAIR (par défaut) */
:root:not([data-theme="dark"]) {
  color: #212529;
}

:root:not([data-theme="dark"]) *:not(.btn):not(.badge):not(.alert) {
  color: inherit;
}

:root:not([data-theme="dark"]) h1,
:root:not([data-theme="dark"]) h2,
:root:not([data-theme="dark"]) h3,
:root:not([data-theme="dark"]) h4,
:root:not([data-theme="dark"]) h5,
:root:not([data-theme="dark"]) h6 {
  color: #212529;
}

:root:not([data-theme="dark"]) p,
:root:not([data-theme="dark"]) span,
:root:not([data-theme="dark"]) div,
:root:not([data-theme="dark"]) td,
:root:not([data-theme="dark"]) th,
:root:not([data-theme="dark"]) li {
  color: #212529;
}

:root:not([data-theme="dark"]) label {
  color: #212529;
}



/* ===== SIDEBAR ===== */
.sidebar {
  background-color: var(--sidebar-bg) !important;
  border-right: 1px solid var(--border-color);
  transition: background-color 0.3s ease;
  top: 56px; /* Hauteur de la navbar */
  height: calc(100vh - 56px);
  position: fixed;
}

.sidebar .nav-link {
  color: var(--sidebar-text) !important;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar .nav-link:hover {
  background-color: var(--sidebar-hover) !important;
}

.sidebar .nav-link.active {
  background-color: var(--sidebar-hover) !important;
  font-weight: 600;
}

/* ===== CARTES ===== */
.card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  box-shadow: 0 0.125rem 0.25rem var(--shadow);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.card-header {
  background-color: var(--bg-secondary);
  border-bottom-color: var(--border-color);
}

.card-footer {
  background-color: var(--bg-secondary);
  border-top-color: var(--border-color);
}

/* ===== TABLEAUX ===== */
.table {
  --bs-table-bg: var(--table-bg);
  --bs-table-striped-bg: var(--table-stripe);
  --bs-table-hover-bg: var(--table-hover);
  --bs-table-border-color: var(--border-color);
  color: var(--text-primary);
}

.table th {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
}

.table td {
  border-color: var(--border-color);
}

/* ===== FORMULAIRES ===== */
.form-control {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--text-primary);
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.form-control:focus {
  background-color: var(--input-bg);
  border-color: #0d6efd;
  color: var(--text-primary);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--text-primary);
}

.input-group-text {
  background-color: var(--bg-secondary);
  border-color: var(--input-border);
  color: var(--text-primary);
}

/* ===== MODALS ===== */
.modal-content {
  background-color: var(--modal-bg);
  border-color: var(--border-color);
}

.modal-header {
  border-bottom-color: var(--border-color);
}

.modal-footer {
  border-top-color: var(--border-color);
}

/* ===== ALERTES ===== */
.alert {
  border-color: var(--border-color);
}

/* ===== LOGO ET BRANDING ===== */
.navbar-brand {
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  transform: scale(1.05);
  text-decoration: none;
}

.navbar-brand i {
  transition: all 0.3s ease;
}

.navbar-brand:hover i {
  transform: rotate(10deg);
}

/* Icône spéciale pour la caisse */
.nav-link[data-section="pos"] i {
  font-weight: bold;
  text-shadow: 0 0 3px rgba(40, 167, 69, 0.5);
}

/* ===== BOUTON DE THÈME FLOTTANT ===== */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-primary);
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 1050;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px var(--shadow);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px var(--shadow-lg);
}

/* ===== NAVIGATION ===== */
.navbar {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-color);
}

.navbar-brand,
.navbar-nav .nav-link,
.navbar-text {
  color: var(--text-primary) !important;
}

.navbar-brand:hover {
  color: var(--text-primary) !important;
}

/* Boutons dans la navbar */
.navbar .btn {
  border-color: var(--border-color);
}

.navbar .btn:hover {
  background-color: var(--bg-tertiary);
}

/* ===== BREADCRUMB ===== */
.breadcrumb {
  background-color: var(--bg-secondary);
}

.breadcrumb-item a {
  color: var(--text-secondary);
}

/* ===== PAGINATION ===== */
.page-link {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.page-link:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* ===== DROPDOWN ===== */
.dropdown-menu {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.dropdown-item {
  color: var(--text-primary);
}

.dropdown-item:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* ===== BADGES ===== */
.badge {
  color: #fff;
}

/* ===== BORDERS ===== */
.border {
  border-color: var(--border-color) !important;
}

.border-top {
  border-top-color: var(--border-color) !important;
}

.border-bottom {
  border-bottom-color: var(--border-color) !important;
}

.border-start {
  border-left-color: var(--border-color) !important;
}

.border-end {
  border-right-color: var(--border-color) !important;
}

/* ===== TEXTE ===== */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* Corrections spécifiques pour le mode sombre */
[data-theme="dark"] .text-muted {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .text-secondary {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-primary {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .fw-bold,
[data-theme="dark"] .fw-semibold,
[data-theme="dark"] strong,
[data-theme="dark"] b {
  color: var(--text-primary) !important;
}

[data-theme="dark"] small {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .lead {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .display-1,
[data-theme="dark"] .display-2,
[data-theme="dark"] .display-3,
[data-theme="dark"] .display-4,
[data-theme="dark"] .display-5,
[data-theme="dark"] .display-6 {
  color: var(--text-primary) !important;
}

/* ===== BACKGROUNDS ===== */
.bg-light {
  background-color: var(--bg-secondary) !important;
}

.bg-white {
  background-color: var(--bg-primary) !important;
}

/* ===== ANIMATIONS ===== */
.theme-transition {
  transition: all 0.3s ease;
}

/* ===== STYLES SPÉCIAUX POUR LE MODE SOMBRE ===== */

/* Améliorer la lisibilité des liens en mode sombre */
[data-theme="dark"] a {
  color: #66b3ff;
}

[data-theme="dark"] a:hover {
  color: #99ccff;
}

/* Améliorer les boutons en mode sombre */
[data-theme="dark"] .btn-outline-secondary {
  color: #b0b0b0 !important;
  border-color: #555555;
}

[data-theme="dark"] .btn-outline-secondary:hover {
  background-color: #555555;
  border-color: #666666;
  color: #ffffff !important;
}

[data-theme="dark"] .btn {
  color: inherit;
}

[data-theme="dark"] .btn-primary {
  color: #ffffff !important;
}

[data-theme="dark"] .btn-secondary {
  color: #ffffff !important;
}

[data-theme="dark"] .btn-success {
  color: #ffffff !important;
}

[data-theme="dark"] .btn-danger {
  color: #ffffff !important;
}

[data-theme="dark"] .btn-warning {
  color: #000000 !important;
}

[data-theme="dark"] .btn-info {
  color: #000000 !important;
}

[data-theme="dark"] .btn-light {
  color: #000000 !important;
}

[data-theme="dark"] .btn-dark {
  color: #ffffff !important;
}

/* Améliorer les badges en mode sombre */
[data-theme="dark"] .badge.bg-info {
  background-color: #0dcaf0 !important;
  color: #000000;
}

[data-theme="dark"] .badge.bg-warning {
  background-color: #ffc107 !important;
  color: #000000;
}

/* Améliorer les alertes en mode sombre */
[data-theme="dark"] .alert-warning {
  background-color: #664d03;
  border-color: #997404;
  color: #ffecb5;
}

[data-theme="dark"] .alert-info {
  background-color: #055160;
  border-color: #087990;
  color: #b6effb;
}

[data-theme="dark"] .alert-success {
  background-color: #0f5132;
  border-color: #146c43;
  color: #d1e7dd;
}

[data-theme="dark"] .alert-danger {
  background-color: #58151c;
  border-color: #842029;
  color: #f8d7da;
}

/* Améliorer les spinners en mode sombre */
[data-theme="dark"] .spinner-border {
  color: #66b3ff;
}

/* Améliorer les tooltips en mode sombre */
[data-theme="dark"] .tooltip .tooltip-inner {
  background-color: #404040;
  color: #ffffff;
}

/* Corrections pour les icônes et éléments spéciaux */
[data-theme="dark"] .bi {
  color: inherit !important;
}

[data-theme="dark"] .navbar-brand {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .nav-link {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .breadcrumb-item {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .breadcrumb-item.active {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .list-group-item {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-text {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-subtitle {
  color: var(--text-secondary) !important;
}

/* Corrections pour les éléments de formulaire */
[data-theme="dark"] .form-label {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .form-text {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .form-check-label {
  color: var(--text-primary) !important;
}

/* Corrections pour les tableaux */
[data-theme="dark"] .table caption {
  color: var(--text-secondary) !important;
}

/* Corrections pour les modals */
[data-theme="dark"] .modal-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .modal-body {
  color: var(--text-primary) !important;
}

/* Corrections pour les alertes */
[data-theme="dark"] .alert {
  border-width: 1px;
}

/* Corrections pour les éléments personnalisés */
[data-theme="dark"] .text-center {
  color: inherit !important;
}

[data-theme="dark"] .text-start {
  color: inherit !important;
}

[data-theme="dark"] .text-end {
  color: inherit !important;
}

/* ===== CONTENU PRINCIPAL ===== */
main {
  margin-top: 56px; /* Hauteur de la navbar */
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .theme-toggle {
    top: 66px; /* Décaler pour la navbar */
    right: 10px;
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .sidebar {
    position: relative;
    top: 0;
    height: auto;
  }

  main {
    margin-top: 0;
  }

  .navbar-brand {
    font-size: 1.5rem !important;
  }

  .navbar-brand i {
    font-size: 1.3rem !important;
  }
}
