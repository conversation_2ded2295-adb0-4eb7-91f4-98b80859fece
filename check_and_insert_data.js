// Script pour vérifier et insérer des données de test
const mysql = require('mysql2/promise');

async function checkAndInsertData() {
  let connection;

  try {
    // Connexion à la base de données
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '', // Mot de passe vide selon la config
      database: 'gesticom' // Base de données selon la config
    });

    console.log('✅ Connexion à la base de données réussie');

    // Vérifier si la table invoice_items existe
    const [tables] = await connection.execute("SHOW TABLES LIKE 'invoice_items'");

    if (tables.length === 0) {
      console.log('❌ La table invoice_items n\'existe pas');
      return;
    }

    console.log('✅ La table invoice_items existe');

    // Vérifier le contenu actuel
    const [existingItems] = await connection.execute('SELECT COUNT(*) as count FROM invoice_items');
    console.log(`📊 Nombre de lignes existantes: ${existingItems[0].count}`);

    // Vérifier les factures existantes
    const [invoices] = await connection.execute('SELECT id, invoice_number FROM invoices ORDER BY id');
    console.log(`📄 Factures existantes: ${invoices.length}`);
    invoices.forEach(inv => {
      console.log(`  - ID ${inv.id}: ${inv.invoice_number}`);
    });

    // Supprimer les données existantes et insérer de nouvelles données
    console.log('🗑️ Suppression des anciennes données...');
    await connection.execute('DELETE FROM invoice_items');

    console.log('📦 Insertion de nouvelles données de test...');

    // Insérer des données seulement pour les factures existantes
    for (const invoice of invoices) {
      console.log(`📦 Ajout de lignes pour la facture ${invoice.id}...`);

      if (invoice.id === 1) {
        // Données pour la facture 1
        await connection.execute(`
          INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
          (1, 1, 1, 89999.99, 0, 19, 89999.99),
          (1, 3, 2, 19999.99, 0, 19, 39999.98)
        `);
      }
    }

    // Vérifier l'insertion
    const [newItems] = await connection.execute('SELECT COUNT(*) as count FROM invoice_items');
    console.log(`✅ ${newItems[0].count} lignes de produits insérées`);

    // Vérifier les données pour la facture 1
    const [items1] = await connection.execute(`
      SELECT ii.*, p.name as productName
      FROM invoice_items ii
      LEFT JOIN products p ON ii.product_id = p.id
      WHERE ii.invoice_id = 1
    `);

    console.log(`📦 Lignes pour la facture 1: ${items1.length}`);
    items1.forEach(item => {
      console.log(`  - ${item.productName}: ${item.quantity} x ${item.unit_price} = ${item.total}`);
    });

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkAndInsertData();
