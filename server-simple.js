const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('./'));

console.log('🚀 Démarrage du serveur simple...');

// Données de test
const testData = {
  products: [
    { id: 1, name: "Ordinateur portable", description: "PC portable haute performance", price: 89999.99, stockQuantity: 15, category: "Informatique", barcode: "123456789", sku: "ORD-001", minStockLevel: 5 },
    { id: 2, name: "Smartphone", description: "Téléphone intelligent", price: 49999.99, stockQuantity: 25, category: "Téléphonie", barcode: "987654321", sku: "TEL-001", minStockLevel: 10 },
    { id: 3, name: "Écran 24 pouces", description: "Moniteur LED 24 pouces", price: 19999.99, stockQuantity: 10, category: "Informatique", barcode: "*********", sku: "ECR-001", minStockLevel: 3 },
    { id: 4, name: "Souris sans fil", description: "Souris optique sans fil", price: 2999.99, stockQuantity: 50, category: "Accessoires", barcode: "*********", sku: "SOU-001", minStockLevel: 15 }
  ],
  clients: [
    { id: 1, name: "Entreprise ABC", contactPerson: "Jean Dupont", email: "<EMAIL>", phone: "021 23 45 67", address: "123 Rue du Commerce", city: "Alger", postalCode: "16000", country: "Algérie", taxId: "DZ123456789", notes: "" },
    { id: 2, name: "Société XYZ", contactPerson: "Marie Martin", email: "<EMAIL>", phone: "041 98 76 54", address: "456 Avenue de la Liberté", city: "Oran", postalCode: "31000", country: "Algérie", taxId: "DZ987654321", notes: "" },
    { id: 3, name: "Client Particulier", contactPerson: "Ahmed Benali", email: "<EMAIL>", phone: "031 45 67 89", address: "789 Boulevard des Martyrs", city: "Constantine", postalCode: "25000", country: "Algérie", taxId: "DZ*********", notes: "" }
  ]
};

// ===== ROUTES D'AUTHENTIFICATION =====

app.post('/auth/login', (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('🔐 Tentative de connexion:', username);

    if (username === 'admin' && password === 'admin') {
      const token = 'admin-token-' + Math.random().toString(36).substring(2);
      
      res.json({
        success: true,
        token: token,
        username: username,
        role: 'admin',
        id: 1,
        message: 'Connexion réussie'
      });
      
      console.log('✅ Connexion réussie pour:', username);
    } else {
      res.status(401).json({
        success: false,
        error: 'Identifiants incorrects'
      });
      
      console.log('❌ Échec de connexion pour:', username);
    }
  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur serveur lors de l\'authentification'
    });
  }
});

// ===== ROUTES API =====

app.get('/api/products', (req, res) => {
  console.log('🔍 Récupération des produits...');
  res.json(testData.products);
});

app.get('/api/clients', (req, res) => {
  console.log('🔍 Récupération des clients...');
  res.json(testData.clients);
});

app.get('/api/suppliers', (req, res) => {
  console.log('🔍 Récupération des fournisseurs...');
  res.json([
    { id: 1, name: "Tech Supplies Inc.", contactPerson: "John Smith", email: "<EMAIL>", phone: "021 11 22 33", address: "123 Tech Street", city: "Alger", postalCode: "16000", country: "Algérie", taxId: "DZ111222333", notes: "" },
    { id: 2, name: "Global Electronics", contactPerson: "Sarah Johnson", email: "<EMAIL>", phone: "041 44 55 66", address: "45 Electronics Avenue", city: "Oran", postalCode: "31000", country: "Algérie", taxId: "DZ444555666", notes: "" },
    { id: 3, name: "Office Solutions", contactPerson: "Mike Wilson", email: "<EMAIL>", phone: "031 77 88 99", address: "78 Office Boulevard", city: "Constantine", postalCode: "25000", country: "Algérie", taxId: "DZ777888999", notes: "" }
  ]);
});

// Routes spécifiques
app.get('/', (req, res) => {
  res.redirect('/login.html');
});

app.get('/login.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'login.html'));
});

app.get('/index.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Route par défaut
app.get('*', (req, res) => {
  if (req.path.includes('.')) {
    res.status(404).send('Fichier non trouvé');
  } else {
    res.redirect('/login.html');
  }
});

// Démarrer le serveur
app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Serveur démarré avec succès !`);
  console.log(`📍 Accès local: http://localhost:${port}`);
  console.log(`🔐 Identifiants de test: admin / admin`);
  console.log(`\n💡 Pour arrêter le serveur, appuyez sur Ctrl+C`);
});
