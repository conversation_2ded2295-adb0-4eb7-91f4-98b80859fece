<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Mode Sombre - GestiCom</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Custom CSS -->
  <link href="css/style.css" rel="stylesheet">
  <!-- Theme CSS -->
  <link href="css/themes.css" rel="stylesheet">
  
  <!-- Script pour appliquer le mode sombre immédiatement -->
  <script>
    // Appliquer le mode sombre par défaut immédiatement pour éviter le flash
    (function() {
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        // Première visite : définir le mode sombre par défaut
        localStorage.setItem('theme', 'dark');
        document.documentElement.setAttribute('data-theme', 'dark');
        console.log('🌙 Mode sombre défini par défaut (première visite)');
      } else if (savedTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
        console.log('🌙 Mode sombre appliqué depuis localStorage');
      }
    })();
  </script>
</head>
<body>
  <div class="container mt-5">
    <div class="row">
      <div class="col-12">
        <h1>Test du Mode Sombre</h1>
        <p>Cette page teste si le mode sombre fonctionne correctement.</p>
        
        <div class="card">
          <div class="card-header">
            <h5>Carte de test</h5>
          </div>
          <div class="card-body">
            <p>Ce texte devrait être blanc en mode sombre.</p>
            <button class="btn btn-primary">Bouton Primary</button>
            <button class="btn btn-secondary">Bouton Secondary</button>
            <button id="toggleTheme" class="btn btn-outline-success">Basculer le thème</button>
          </div>
        </div>
        
        <div class="mt-3">
          <p>Thème actuel : <span id="currentTheme">Chargement...</span></p>
          <p>Attribut data-theme : <span id="dataTheme">Chargement...</span></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Theme JS -->
  <script src="js/theme.js"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Afficher les informations de thème
      function updateThemeInfo() {
        const currentThemeSpan = document.getElementById('currentTheme');
        const dataThemeSpan = document.getElementById('dataTheme');
        
        if (window.themeManager) {
          currentThemeSpan.textContent = window.themeManager.getCurrentTheme();
        } else {
          currentThemeSpan.textContent = 'ThemeManager non disponible';
        }
        
        const dataTheme = document.documentElement.getAttribute('data-theme');
        dataThemeSpan.textContent = dataTheme || 'aucun (mode clair)';
      }
      
      // Mettre à jour les infos au chargement
      setTimeout(updateThemeInfo, 100);
      
      // Bouton pour basculer le thème
      document.getElementById('toggleTheme').addEventListener('click', function() {
        if (window.themeManager) {
          window.themeManager.toggleTheme();
          setTimeout(updateThemeInfo, 100);
        }
      });
      
      // Écouter les changements de thème
      document.addEventListener('themeChanged', function() {
        setTimeout(updateThemeInfo, 100);
      });
    });
  </script>
</body>
</html>
