// Script de débogage pour la page Super Admin
console.log('🔍 Script de débogage chargé');

// Fonction globale pour gérer la sauvegarde (appelée par onclick)
function handleSaveUser() {
  console.log('🎯 handleSaveUser() appelée !');

  if (typeof saveUser === 'function') {
    console.log('📞 Appel de saveUser()...');
    saveUser();
  } else {
    console.error('❌ Fonction saveUser non trouvée, création d\'une version de secours...');
    saveUserFallback();
  }
}

// Version de secours de la fonction saveUser
async function saveUserFallback() {
  console.log('🔄 Début de la sauvegarde utilisateur (version de secours)...');

  const form = document.getElementById('userForm');
  if (!form) {
    console.error('❌ Formulaire userForm non trouvé');
    alert('Erreur: Formulaire non trouvé');
    return;
  }

  console.log('✅ Formulaire trouvé, validation...');
  if (!form.checkValidity()) {
    console.log('❌ Formulaire invalide');
    form.reportValidity();
    return;
  }

  console.log('✅ Formulaire valide, récupération des données...');

  const userId = document.getElementById('userId').value;
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  // Vérifier les mots de passe
  if (password && password !== confirmPassword) {
    alert('Les mots de passe ne correspondent pas');
    return;
  }

  if (!userId && (!password || password.length < 6)) {
    alert('Le mot de passe doit contenir au moins 6 caractères');
    return;
  }

  const userData = {
    username: document.getElementById('username').value,
    email: document.getElementById('email').value,
    role: document.getElementById('userRole').value,
    fullName: document.getElementById('fullName').value,
    phone: document.getElementById('phone').value,
    status: document.getElementById('userActive').checked ? 'active' : 'inactive'
  };

  if (password && password.trim() !== '') {
    userData.password = password;
  }

  console.log('📝 Données à envoyer:', userData);

  try {
    let response;

    if (userId) {
      // Modification
      console.log('📝 Modification utilisateur ID:', userId);
      response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });
    } else {
      // Création
      console.log('📝 Création nouvel utilisateur:', userData.username);
      response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });
    }

    console.log('📡 Statut de la réponse:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Erreur HTTP: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Résultat:', result);

    alert(result.message || (userId ? 'Utilisateur modifié avec succès' : 'Utilisateur créé avec succès'));

    // Fermer le modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
    if (modal) {
      modal.hide();
    }

    // Recharger la liste des utilisateurs
    if (typeof loadUsers === 'function') {
      await loadUsers();
    } else {
      console.log('⚠️ Fonction loadUsers non trouvée, rechargement de la page...');
      window.location.reload();
    }

  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde:', error);
    alert('Erreur lors de la sauvegarde: ' + error.message);
  }
}

// Fonction pour vérifier les éléments du DOM
function checkDOMElements() {
  console.log('🔍 Vérification des éléments DOM...');

  const elements = [
    'saveUserBtn',
    'userForm',
    'username',
    'email',
    'password',
    'confirmPassword',
    'userRole',
    'fullName',
    'phone',
    'userActive',
    'addUserModal'
  ];

  elements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
      console.log(`✅ ${id} trouvé:`, element);
    } else {
      console.error(`❌ ${id} NON TROUVÉ`);
    }
  });
}

// Fonction pour attacher manuellement le gestionnaire
function attachSaveHandler() {
  console.log('🔧 Attachement manuel du gestionnaire...');

  const saveBtn = document.getElementById('saveUserBtn');
  if (saveBtn) {
    // Supprimer les anciens gestionnaires
    saveBtn.replaceWith(saveBtn.cloneNode(true));

    // Récupérer le nouveau bouton
    const newSaveBtn = document.getElementById('saveUserBtn');

    newSaveBtn.addEventListener('click', function(e) {
      console.log('🎯 Clic détecté sur le bouton Enregistrer !');
      e.preventDefault();

      // Appeler la fonction de sauvegarde
      if (typeof saveUser === 'function') {
        console.log('📞 Appel de saveUser()...');
        saveUser();
      } else {
        console.error('❌ Fonction saveUser non trouvée');
      }
    });

    console.log('✅ Gestionnaire attaché avec succès');
  } else {
    console.error('❌ Bouton saveUserBtn non trouvé');
  }
}

// Fonction pour tester la sauvegarde directement
function testSaveDirectly() {
  console.log('🧪 Test direct de sauvegarde...');

  // Remplir le formulaire avec des données de test
  document.getElementById('username').value = 'test_debug';
  document.getElementById('email').value = '<EMAIL>';
  document.getElementById('password').value = 'test123';
  document.getElementById('confirmPassword').value = 'test123';
  document.getElementById('userRole').value = 'viewer';
  document.getElementById('fullName').value = 'Test Debug User';
  document.getElementById('phone').value = '0123456789';
  document.getElementById('userActive').checked = true;

  console.log('📝 Formulaire rempli, appel de saveUser()...');

  if (typeof saveUser === 'function') {
    saveUser();
  } else {
    console.error('❌ Fonction saveUser non disponible');
  }
}

// Attendre que la page soit chargée
setTimeout(() => {
  console.log('🔍 Démarrage du débogage...');
  checkDOMElements();
  attachSaveHandler();

  // Ajouter des boutons de test dans la console
  window.debugSuperAdmin = {
    checkDOM: checkDOMElements,
    attachHandler: attachSaveHandler,
    testSave: testSaveDirectly
  };

  console.log('🎮 Fonctions de débogage disponibles:');
  console.log('   - debugSuperAdmin.checkDOM()');
  console.log('   - debugSuperAdmin.attachHandler()');
  console.log('   - debugSuperAdmin.testSave()');

}, 2000);
