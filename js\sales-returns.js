// Objet pour gérer les fonctionnalités des retours clients
const salesReturnsManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/sales-returns');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        {
          id: 1,
          returnNumber: 'RET-001',
          date: '2024-05-20',
          clientId: 1,
          clientName: 'Client Particulier',
          originalInvoiceNumber: 'FACT-001',
          reason: 'Produit défectueux',
          total: 89999.99,
          status: 'pending'
        },
        {
          id: 2,
          returnNumber: 'RET-002',
          date: '2024-05-22',
          clientId: 2,
          clientName: 'Entreprise ABC',
          originalInvoiceNumber: 'FACT-002',
          reason: 'Erreur de commande',
          total: 49999.99,
          status: 'approved'
        }
      ];
    }
  },

  getById: async (id) => {
    try {
      const response = await fetch(`/api/sales-returns/${id}`);
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de récupération");
      const testReturns = await salesReturnsManager.getAll();
      return testReturns.find(returnItem => returnItem.id === parseInt(id)) || null;
    }
  },

  add: async (returnData) => {
    try {
      const response = await fetch('/api/sales-returns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(returnData)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      return {
        ...returnData,
        id: Math.floor(Math.random() * 1000) + 10,
        returnNumber: `RET-${String(Math.floor(Math.random() * 1000) + 100).padStart(3, '0')}`
      };
    }
  },

  update: async (id, data) => {
    try {
      const response = await fetch(`/api/sales-returns/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      return { ...data, id: parseInt(id) };
    }
  },

  delete: async (id) => {
    try {
      await fetch(`/api/sales-returns/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return true;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  }
};

// Fonction d'initialisation pour la section retours clients
function initSalesReturnsSection() {
  console.log("Initialisation de la section retours clients");

  // Charger la liste des retours
  loadSalesReturnsList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('saveSalesReturnBtn')?.addEventListener('click', saveSalesReturn);
  document.getElementById('searchSalesReturn')?.addEventListener('input', filterSalesReturns);

  // Gestionnaire pour le bouton "Nouveau retour"
  const newReturnBtn = document.querySelector('[data-bs-target="#addSalesReturnModal"]');
  if (newReturnBtn) {
    newReturnBtn.addEventListener('click', () => {
      openSalesReturnModal();
    });
  }

  // Charger les données pour les formulaires
  loadClientsForForm();
  loadProductsForForm();
}

// Fonction pour charger et afficher la liste des retours clients
async function loadSalesReturnsList() {
  try {
    console.log("Chargement de la liste des retours clients...");
    const returns = await salesReturnsManager.getAll();
    const tableBody = document.getElementById('salesReturnsTableBody');

    if (!tableBody) {
      console.error("Table des retours clients non trouvée");
      return;
    }

    tableBody.innerHTML = returns.map(returnItem => `
      <tr>
        <td>${returnItem.returnNumber}</td>
        <td>${new Date(returnItem.date).toLocaleDateString()}</td>
        <td>${returnItem.clientName}</td>
        <td>${returnItem.originalInvoiceNumber}</td>
        <td>${returnItem.reason}</td>
        <td>${returnItem.total.toFixed(2)} DA</td>
        <td>
          <span class="badge ${getStatusBadgeClass(returnItem.status)}">
            ${getStatusLabel(returnItem.status)}
          </span>
        </td>
        <td>
          <button class="btn btn-sm btn-outline-info view-return" data-id="${returnItem.id}" title="Aperçu">
            <i class="bi bi-eye"></i>
          </button>
          <button class="btn btn-sm btn-outline-success print-return" data-id="${returnItem.id}" title="Imprimer">
            <i class="bi bi-printer"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger delete-return" data-id="${returnItem.id}" title="Supprimer">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      </tr>
    `).join('');

    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addSalesReturnActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des retours clients:', error);
    const tableBody = document.getElementById('salesReturnsTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Erreur lors du chargement des retours clients</td></tr>';
    }
  }
}

// Fonction pour obtenir la classe CSS du badge de statut
function getStatusBadgeClass(status) {
  switch (status) {
    case 'pending': return 'bg-warning';
    case 'approved': return 'bg-success';
    case 'rejected': return 'bg-danger';
    case 'processed': return 'bg-info';
    default: return 'bg-secondary';
  }
}

// Fonction pour obtenir le libellé du statut
function getStatusLabel(status) {
  switch (status) {
    case 'pending': return 'En attente';
    case 'approved': return 'Approuvé';
    case 'rejected': return 'Rejeté';
    case 'processed': return 'Traité';
    default: return status;
  }
}

// Fonction pour filtrer les retours clients
function filterSalesReturns() {
  const searchTerm = document.getElementById('searchSalesReturn')?.value.toLowerCase() || '';
  const rows = document.querySelectorAll('#salesReturnsTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour sauvegarder un retour client
async function saveSalesReturn() {
  // À implémenter selon les besoins
  console.log("Sauvegarde du retour client");
  showSuccess('Retour client sauvegardé avec succès');
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addSalesReturnActionHandlers() {
  // Gestionnaires pour les boutons de visualisation
  document.querySelectorAll('.view-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      await viewSalesReturn(returnId);
    });
  });

  // Gestionnaires pour les boutons d'impression
  document.querySelectorAll('.print-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      await printSalesReturn(returnId);
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      const returnNumber = e.currentTarget.closest('tr').querySelector('td:nth-child(1)').textContent;

      const confirmed = await showConfirm(
        `Êtes-vous sûr de vouloir supprimer le retour client "${returnNumber}" ?`,
        'Supprimer le retour client',
        { confirmText: 'Supprimer', cancelText: 'Annuler', type: 'error' }
      );

      if (confirmed) {
        await deleteSalesReturn(returnId);
      }
    });
  });
}

// Fonction pour afficher un message de succès
function showSuccess(message) {
  // Utiliser la fonction globale si elle existe, sinon console.log
  if (typeof window.showSuccess === 'function') {
    window.showSuccess(message);
  } else {
    console.log('✅ ' + message);
  }
}

// Fonction pour afficher un message d'erreur
function showError(message) {
  // Utiliser la fonction globale si elle existe, sinon console.error
  if (typeof window.showError === 'function') {
    window.showError(message);
  } else {
    console.error('❌ ' + message);
  }
}

// Fonction pour visualiser un retour client
async function viewSalesReturn(returnId) {
  try {
    const returnData = await salesReturnsManager.getById(returnId);
    if (!returnData) {
      showError('Retour client non trouvé');
      return;
    }

    // Créer un modal de visualisation
    const modalHtml = `
      <div class="modal fade" id="viewSalesReturnModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="bi bi-eye me-2"></i>Détails du retour ${returnData.returnNumber}
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row mb-3">
                <div class="col-md-6">
                  <strong>Date:</strong> ${new Date(returnData.date).toLocaleDateString()}
                </div>
                <div class="col-md-6">
                  <strong>Client:</strong> ${returnData.clientName}
                </div>
              </div>
              <div class="row mb-3">
                <div class="col-md-6">
                  <strong>Facture associée:</strong> ${returnData.originalInvoiceNumber || 'N/A'}
                </div>
                <div class="col-md-6">
                  <strong>Statut:</strong>
                  <span class="badge ${getStatusBadgeClass(returnData.status)}">
                    ${getStatusLabel(returnData.status)}
                  </span>
                </div>
              </div>
              <div class="mb-3">
                <strong>Motif du retour:</strong><br>
                ${returnData.reason}
              </div>
              <div class="mb-3">
                <strong>Total:</strong> <span class="fs-5 text-primary">${returnData.total.toFixed(2)} DA</span>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
              <button type="button" class="btn btn-primary" onclick="editSalesReturn(${returnData.id})">
                <i class="bi bi-pencil me-2"></i>Modifier
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Supprimer le modal existant s'il y en a un
    const existingModal = document.getElementById('viewSalesReturnModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Ajouter le nouveau modal au DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('viewSalesReturnModal'));
    modal.show();

    // Nettoyer le DOM quand le modal est fermé
    document.getElementById('viewSalesReturnModal').addEventListener('hidden.bs.modal', function() {
      this.remove();
    });

  } catch (error) {
    console.error('Erreur lors de la visualisation du retour:', error);
    showError('Erreur lors de la visualisation du retour');
  }
}

// Fonction pour éditer un retour client
async function editSalesReturn(returnId) {
  try {
    const returnData = await salesReturnsManager.getById(returnId);
    if (!returnData) {
      showError('Retour client non trouvé');
      return;
    }

    // Remplir le formulaire avec les données existantes
    document.getElementById('salesReturnId').value = returnData.id;
    document.getElementById('salesReturnDate').value = returnData.date;
    document.getElementById('salesReturnClient').value = returnData.clientId;
    document.getElementById('salesReturnInvoice').value = returnData.originalInvoiceNumber || '';
    document.getElementById('salesReturnStatus').value = returnData.status;
    document.getElementById('salesReturnReason').value = returnData.reason;

    // Changer le titre du modal
    document.getElementById('addSalesReturnModalLabel').textContent = `Modifier le retour ${returnData.returnNumber}`;

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('addSalesReturnModal'));
    modal.show();

  } catch (error) {
    console.error('Erreur lors de l\'édition du retour:', error);
    showError('Erreur lors de l\'édition du retour');
  }
}

// Fonction pour imprimer un retour client
async function printSalesReturn(returnId) {
  try {
    console.log('🖨️ Impression du retour client:', returnId);

    const returnData = await salesReturnsManager.getById(returnId);

    if (!returnData) {
      showError('Retour client non trouvé');
      return;
    }

    // Créer une fenêtre d'impression
    const printWindow = window.open('', '_blank');
    printWindow.document.write(generateSalesReturnPrintHTML(returnData));
    printWindow.document.close();

    // Attendre le chargement puis imprimer
    printWindow.onload = function() {
      printWindow.print();
      printWindow.close();
    };

    showSuccess('Retour client envoyé vers l\'imprimante');
  } catch (error) {
    console.error('❌ Erreur lors de l\'impression:', error);
    showError('Erreur lors de l\'impression du retour client');
  }
}

// Fonction pour générer le HTML d'impression
function generateSalesReturnPrintHTML(returnData) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Retour Client ${returnData.returnNumber}</title>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        @media print {
          .no-print { display: none !important; }
          body { font-size: 12px; }
          .table { font-size: 11px; }
        }
        body { font-family: Arial, sans-serif; }
        .return-document { max-width: 800px; margin: 0 auto; padding: 20px; }
      </style>
    </head>
    <body>
      <div class="return-document">
        <div class="row mb-4">
          <div class="col-md-6">
            <h3 class="text-primary">GestiCom</h3>
            <p class="mb-0">123 Rue de l'Entreprise</p>
            <p class="mb-0">16000 Alger, Algérie</p>
            <p class="mb-0">Tél: +213 21 XX XX XX</p>
          </div>
          <div class="col-md-6 text-end">
            <h4>RETOUR CLIENT</h4>
            <p class="mb-1"><strong>N°:</strong> ${returnData.returnNumber}</p>
            <p class="mb-1"><strong>Date:</strong> ${new Date(returnData.date).toLocaleDateString('fr-FR')}</p>
            <p class="mb-1"><strong>Statut:</strong> <span class="badge ${getStatusBadgeClass(returnData.status)}">${getStatusLabel(returnData.status)}</span></p>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <h6>Client:</h6>
            <p class="mb-0"><strong>${returnData.clientName}</strong></p>
          </div>
          <div class="col-md-6">
            <h6>Facture associée:</h6>
            <p class="mb-0">${returnData.originalInvoiceNumber || 'N/A'}</p>
          </div>
        </div>

        <div class="mb-4">
          <h6>Motif du retour:</h6>
          <p>${returnData.reason}</p>
        </div>

        <div class="row mb-4">
          <div class="col-md-6 offset-md-6">
            <div class="border p-3">
              <div class="d-flex justify-content-between mb-2">
                <span><strong>Total du retour:</strong></span>
                <span><strong>${returnData.total.toFixed(2)} DA</strong></span>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-5">
          <div class="col-md-6">
            <div class="border p-3">
              <p class="mb-1"><strong>Signature du client:</strong></p>
              <div style="height: 60px;"></div>
              <p class="mb-0 text-center">Date et signature</p>
            </div>
          </div>
          <div class="col-md-6">
            <div class="border p-3">
              <p class="mb-1"><strong>Signature du responsable:</strong></p>
              <div style="height: 60px;"></div>
              <p class="mb-0 text-center">Date et signature</p>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Fonction pour supprimer un retour client
async function deleteSalesReturn(returnId) {
  try {
    console.log(`🗑️ Suppression du retour client ${returnId}...`);

    // Appeler la fonction de suppression
    const result = await salesReturnsManager.delete(returnId);
    console.log('Résultat de la suppression:', result);

    if (result) {
      // Recharger la liste
      await loadSalesReturnsList();

      // Afficher le message de succès
      showSuccess('Retour client supprimé avec succès');
    } else {
      showError('Erreur lors de la suppression du retour client');
    }

  } catch (error) {
    console.error('Erreur lors de la suppression du retour:', error);
    showError('Erreur lors de la suppression du retour: ' + error.message);
  }
}

// Fonction pour ouvrir le modal de nouveau retour
function openSalesReturnModal() {
  // Réinitialiser le formulaire
  document.getElementById('salesReturnForm').reset();
  document.getElementById('salesReturnId').value = '';

  // Remettre le titre par défaut
  document.getElementById('addSalesReturnModalLabel').textContent = 'Nouveau retour client';

  // Définir la date d'aujourd'hui par défaut
  const today = new Date().toISOString().split('T')[0];
  document.getElementById('salesReturnDate').value = today;
}

// Fonction pour charger les clients dans le formulaire
async function loadClientsForForm() {
  try {
    // Utiliser le gestionnaire de clients s'il existe
    if (typeof clientManager !== 'undefined') {
      const clients = await clientManager.getAll();
      const select = document.getElementById('salesReturnClient');
      if (select) {
        select.innerHTML = '<option value="">Sélectionner un client</option>';
        clients.forEach(client => {
          select.innerHTML += `<option value="${client.id}">${client.name}</option>`;
        });
      }
    }
  } catch (error) {
    console.error('Erreur lors du chargement des clients:', error);
  }
}

// Fonction pour charger les produits dans le formulaire
async function loadProductsForForm() {
  try {
    // Utiliser le gestionnaire de produits s'il existe
    if (typeof productManager !== 'undefined') {
      const products = await productManager.getAll();
      const selects = document.querySelectorAll('.salesReturnProduct');
      selects.forEach(select => {
        select.innerHTML = '<option value="">Sélectionner un produit</option>';
        products.forEach(product => {
          select.innerHTML += `<option value="${product.id}" data-price="${product.price}">${product.name}</option>`;
        });
      });
    }
  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
  }
}
