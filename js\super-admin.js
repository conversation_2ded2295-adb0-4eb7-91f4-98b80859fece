// Gestionnaire pour la page Super Admin
const superAdminManager = {
  users: [],
  currentUser: null,
  networkConfig: {},
  systemConfig: {}
};

// Fonction d'initialisation de la section Super Admin
function initSuperAdminSection() {
  console.log("🔧 Initialisation de la section Super Admin");

  // Vérifier les permissions
  if (!checkSuperAdminPermissions()) {
    showError('Accès refusé. Permissions Super Admin requises.');
    return;
  }

  // Attendre que le DOM soit complètement chargé
  setTimeout(() => {
    console.log("🔧 Configuration des gestionnaires d'événements (après timeout)");
    setupEventHandlers();
  }, 100);

  // Charger les données initiales
  loadDashboardStats();
  loadUsers();
  loadNetworkConfig();
  loadSystemConfig();

  // Actualiser les données périodiquement
  setInterval(loadDashboardStats, 30000); // Toutes les 30 secondes
}

// Vérifier les permissions Super Admin
function checkSuperAdminPermissions() {
  // Pour l'instant, on simule la vérification
  // Dans un vrai système, on vérifierait le token JWT ou la session
  const userRole = localStorage.getItem('userRole') || 'admin';
  return userRole === 'superadmin' || userRole === 'admin';
}

// Charger les statistiques du tableau de bord
async function loadDashboardStats() {
  try {
    // Simuler des données pour l'instant
    const stats = {
      totalUsers: 5,
      connectedUsers: 2,
      serverStatus: 'En ligne',
      networkIP: '************'
    };

    document.getElementById('total-users').textContent = stats.totalUsers;
    document.getElementById('connected-users').textContent = stats.connectedUsers;
    document.getElementById('server-status').textContent = stats.serverStatus;
    document.getElementById('network-ip').textContent = stats.networkIP;

  } catch (error) {
    console.error('Erreur lors du chargement des statistiques:', error);
  }
}

// Charger la liste des utilisateurs
async function loadUsers() {
  try {
    console.log('🔍 Chargement des utilisateurs depuis l\'API...');

    const response = await fetch('/api/users');
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    const users = await response.json();
    console.log(`✅ ${users.length} utilisateurs chargés`);

    // Adapter les données pour l'affichage
    const adaptedUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      status: user.status,
      lastLogin: user.last_login || 'Jamais',
      fullName: user.full_name || user.username,
      phone: user.phone || ''
    }));

    superAdminManager.users = adaptedUsers;
    displayUsers(adaptedUsers);

    // Mettre à jour les statistiques
    document.getElementById('total-users').textContent = users.length;

  } catch (error) {
    console.error('Erreur lors du chargement des utilisateurs:', error);
    showError('Erreur lors du chargement des utilisateurs: ' + error.message);

    // En cas d'erreur, afficher un message dans le tableau
    const tableBody = document.getElementById('usersTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Erreur lors du chargement des utilisateurs</td></tr>';
    }
  }
}

// Afficher la liste des utilisateurs
function displayUsers(users) {
  const tableBody = document.getElementById('usersTableBody');

  tableBody.innerHTML = users.map(user => `
    <tr>
      <td>${user.id}</td>
      <td>${user.username}</td>
      <td>${user.email}</td>
      <td>
        <span class="badge ${getRoleBadgeClass(user.role)}">
          ${getRoleLabel(user.role)}
        </span>
      </td>
      <td>
        <span class="badge ${user.status === 'active' ? 'bg-success' : 'bg-danger'}">
          ${user.status === 'active' ? 'Actif' : 'Inactif'}
        </span>
      </td>
      <td>${new Date(user.lastLogin).toLocaleString()}</td>
      <td>
        <button class="btn btn-sm btn-outline-primary edit-user" data-id="${user.id}" title="Modifier">
          <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-sm btn-outline-danger delete-user" data-id="${user.id}" title="Supprimer">
          <i class="bi bi-trash"></i>
        </button>
        <button class="btn btn-sm btn-outline-info reset-password" data-id="${user.id}" title="Réinitialiser mot de passe">
          <i class="bi bi-key"></i>
        </button>
      </td>
    </tr>
  `).join('');

  // Ajouter les gestionnaires d'événements
  addUserActionHandlers();
}

// Obtenir la classe CSS pour le badge de rôle
function getRoleBadgeClass(role) {
  switch (role) {
    case 'superadmin': return 'bg-danger';
    case 'admin': return 'bg-warning';
    case 'manager': return 'bg-info';
    case 'cashier': return 'bg-success';
    case 'viewer': return 'bg-secondary';
    default: return 'bg-secondary';
  }
}

// Obtenir le libellé du rôle
function getRoleLabel(role) {
  switch (role) {
    case 'superadmin': return 'Super Admin';
    case 'admin': return 'Administrateur';
    case 'manager': return 'Gestionnaire';
    case 'cashier': return 'Caissier';
    case 'viewer': return 'Consultation';
    default: return role;
  }
}

// Charger la configuration réseau
async function loadNetworkConfig() {
  try {
    // Charger depuis le fichier de configuration
    const response = await fetch('/network-config.json');
    if (response.ok) {
      const config = await response.json();
      superAdminManager.networkConfig = config;

      document.getElementById('serverIP').value = config.serverIP;
      document.getElementById('localURL').textContent = `http://localhost:${config.serverPort}`;
      document.getElementById('networkURL').textContent = config.accessURL;
    }
  } catch (error) {
    console.error('Erreur lors du chargement de la configuration réseau:', error);
  }
}

// Charger la configuration système
function loadSystemConfig() {
  // Charger les paramètres système depuis localStorage ou API
  const config = {
    dbHost: 'localhost',
    dbPort: 3306,
    dbName: 'gesticom',
    dbUser: 'root',
    enableSSL: false,
    enableAuth: true,
    cacheTimeout: 300,
    logLevel: 'info'
  };

  superAdminManager.systemConfig = config;

  // Remplir le formulaire
  document.getElementById('dbHost').value = config.dbHost;
  document.getElementById('dbPort').value = config.dbPort;
  document.getElementById('dbName').value = config.dbName;
  document.getElementById('dbUser').value = config.dbUser;
  document.getElementById('enableSSL').checked = config.enableSSL;
  document.getElementById('enableAuth').checked = config.enableAuth;
  document.getElementById('cacheTimeout').value = config.cacheTimeout;
  document.getElementById('logLevel').value = config.logLevel;
}

// Configurer les gestionnaires d'événements
function setupEventHandlers() {
  console.log('🔧 Configuration des gestionnaires d\'événements...');

  // Bouton sauvegarder utilisateur
  const saveBtn = document.getElementById('saveUserBtn');
  if (saveBtn) {
    console.log('✅ Bouton saveUserBtn trouvé, ajout du gestionnaire...');
    saveBtn.addEventListener('click', saveUser);
  } else {
    console.error('❌ Bouton saveUserBtn non trouvé');
  }

  // Formulaires (avec vérification d'existence)
  const networkForm = document.getElementById('networkConfigForm');
  if (networkForm) {
    networkForm.addEventListener('submit', saveNetworkConfig);
  }

  const systemForm = document.getElementById('systemConfigForm');
  if (systemForm) {
    systemForm.addEventListener('submit', saveSystemConfig);
  }

  // Boutons d'action (avec vérification d'existence)
  const testNetworkBtn = document.getElementById('testNetworkBtn');
  if (testNetworkBtn) {
    testNetworkBtn.addEventListener('click', testNetworkConnectivity);
  }

  const restartServerBtn = document.getElementById('restartServerBtn');
  if (restartServerBtn) {
    restartServerBtn.addEventListener('click', restartServer);
  }

  const refreshMonitoringBtn = document.getElementById('refreshMonitoringBtn');
  if (refreshMonitoringBtn) {
    refreshMonitoringBtn.addEventListener('click', refreshMonitoring);
  }

  const createBackupBtn = document.getElementById('createBackupBtn');
  if (createBackupBtn) {
    createBackupBtn.addEventListener('click', createBackup);
  }

  const restoreBackupBtn = document.getElementById('restoreBackupBtn');
  if (restoreBackupBtn) {
    restoreBackupBtn.addEventListener('click', restoreBackup);
  }

  // Modal utilisateur
  const addUserModal = document.getElementById('addUserModal');
  if (addUserModal) {
    addUserModal.addEventListener('show.bs.modal', resetUserForm);
  }
}

// Ajouter les gestionnaires pour les actions utilisateur
function addUserActionHandlers() {
  // Boutons modifier
  document.querySelectorAll('.edit-user').forEach(button => {
    button.addEventListener('click', (e) => {
      const userId = parseInt(e.currentTarget.getAttribute('data-id'));
      editUser(userId);
    });
  });

  // Boutons supprimer
  document.querySelectorAll('.delete-user').forEach(button => {
    button.addEventListener('click', (e) => {
      const userId = parseInt(e.currentTarget.getAttribute('data-id'));
      deleteUser(userId);
    });
  });

  // Boutons réinitialiser mot de passe
  document.querySelectorAll('.reset-password').forEach(button => {
    button.addEventListener('click', (e) => {
      const userId = parseInt(e.currentTarget.getAttribute('data-id'));
      resetUserPassword(userId);
    });
  });
}

// Réinitialiser le formulaire utilisateur
function resetUserForm() {
  document.getElementById('userForm').reset();
  document.getElementById('userId').value = '';
  document.getElementById('addUserModalLabel').textContent = 'Ajouter Utilisateur';
  document.getElementById('password').required = true;
  document.getElementById('confirmPassword').required = true;
}

// Modifier un utilisateur
function editUser(userId) {
  const user = superAdminManager.users.find(u => u.id === userId);
  if (!user) {
    showError('Utilisateur non trouvé');
    return;
  }

  // Remplir le formulaire
  document.getElementById('userId').value = user.id;
  document.getElementById('username').value = user.username;
  document.getElementById('email').value = user.email;
  document.getElementById('userRole').value = user.role;
  document.getElementById('fullName').value = user.fullName || '';
  document.getElementById('phone').value = user.phone || '';
  document.getElementById('userActive').checked = user.status === 'active';

  // Modifier le titre et rendre le mot de passe optionnel
  document.getElementById('addUserModalLabel').textContent = 'Modifier Utilisateur';
  document.getElementById('password').required = false;
  document.getElementById('confirmPassword').required = false;
  document.getElementById('password').placeholder = 'Laissez vide pour ne pas changer';

  // Afficher le modal
  const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
  modal.show();
}

// Sauvegarder un utilisateur
async function saveUser() {
  console.log('🔄 Début de la sauvegarde utilisateur...');

  const form = document.getElementById('userForm');
  if (!form) {
    console.error('❌ Formulaire userForm non trouvé');
    showError('Erreur: Formulaire non trouvé');
    return;
  }

  console.log('✅ Formulaire trouvé, validation...');
  if (!form.checkValidity()) {
    console.log('❌ Formulaire invalide');
    form.reportValidity();
    return;
  }

  console.log('✅ Formulaire valide, récupération des données...');

  const userId = document.getElementById('userId').value;
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  // Vérifier les mots de passe
  if (password && password !== confirmPassword) {
    showError('Les mots de passe ne correspondent pas');
    return;
  }

  if (!userId && (!password || password.length < 6)) {
    showError('Le mot de passe doit contenir au moins 6 caractères');
    return;
  }

  const userData = {
    username: document.getElementById('username').value,
    email: document.getElementById('email').value,
    role: document.getElementById('userRole').value,
    fullName: document.getElementById('fullName').value,
    phone: document.getElementById('phone').value,
    status: document.getElementById('userActive').checked ? 'active' : 'inactive'
  };

  if (password && password.trim() !== '') {
    userData.password = password;
  }

  try {
    let response;

    if (userId) {
      // Modification
      console.log('📝 Modification utilisateur ID:', userId);
      response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });
    } else {
      // Création
      console.log('📝 Création nouvel utilisateur:', userData.username);
      response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Erreur HTTP: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Résultat:', result);

    showSuccess(result.message || (userId ? 'Utilisateur modifié avec succès' : 'Utilisateur créé avec succès'));

    // Fermer le modal et recharger la liste
    bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
    await loadUsers();

  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde:', error);
    showError('Erreur lors de la sauvegarde: ' + error.message);
  }
}

// Supprimer un utilisateur
function deleteUser(userId) {
  const user = superAdminManager.users.find(u => u.id === userId);
  if (!user) {
    showError('Utilisateur non trouvé');
    return;
  }

  document.getElementById('deleteUserName').textContent = user.username;

  const modal = new bootstrap.Modal(document.getElementById('deleteUserModal'));
  modal.show();

  // Gestionnaire de confirmation
  document.getElementById('confirmDeleteUserBtn').onclick = async () => {
    try {
      console.log('🗑️ Suppression utilisateur ID:', userId);

      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Erreur HTTP: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Utilisateur supprimé');

      showSuccess(result.message || 'Utilisateur supprimé avec succès');
      modal.hide();
      await loadUsers();

    } catch (error) {
      console.error('❌ Erreur lors de la suppression:', error);
      showError('Erreur lors de la suppression: ' + error.message);
    }
  };
}

// Réinitialiser le mot de passe d'un utilisateur
async function resetUserPassword(userId) {
  const user = superAdminManager.users.find(u => u.id === userId);
  if (!user) {
    showError('Utilisateur non trouvé');
    return;
  }

  const confirmed = confirm(`Réinitialiser le mot de passe de ${user.username} ?\nUn nouveau mot de passe temporaire sera généré.`);
  if (!confirmed) return;

  try {
    console.log('🔑 Réinitialisation du mot de passe pour:', user.username);

    const response = await fetch(`/api/users/${userId}/reset-password`, {
      method: 'POST'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Erreur HTTP: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Mot de passe réinitialisé');

    // Afficher le mot de passe temporaire
    alert(`Nouveau mot de passe temporaire pour ${result.username}:\n\n${result.tempPassword}\n\nL'utilisateur devra le changer à sa prochaine connexion.`);

    showSuccess(result.message || 'Mot de passe réinitialisé avec succès');

  } catch (error) {
    console.error('❌ Erreur lors de la réinitialisation:', error);
    showError('Erreur lors de la réinitialisation: ' + error.message);
  }
}

// Générer un mot de passe temporaire
function generateTempPassword() {
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
  let password = '';
  for (let i = 0; i < 8; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// Sauvegarder la configuration réseau
async function saveNetworkConfig(e) {
  e.preventDefault();

  const config = {
    serverIP: document.getElementById('serverIP').value,
    serverPort: parseInt(document.getElementById('serverPort').value),
    maxConnections: parseInt(document.getElementById('maxConnections').value),
    sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
    allowedIPs: document.getElementById('allowedIPs').value.split('\n').filter(ip => ip.trim())
  };

  try {
    console.log('Sauvegarde configuration réseau:', config);
    showSuccess('Configuration réseau sauvegardée. Redémarrage du serveur requis.');
  } catch (error) {
    console.error('Erreur sauvegarde réseau:', error);
    showError('Erreur lors de la sauvegarde de la configuration réseau');
  }
}

// Sauvegarder la configuration système
async function saveSystemConfig(e) {
  e.preventDefault();

  const config = {
    dbHost: document.getElementById('dbHost').value,
    dbPort: parseInt(document.getElementById('dbPort').value),
    dbName: document.getElementById('dbName').value,
    dbUser: document.getElementById('dbUser').value,
    enableSSL: document.getElementById('enableSSL').checked,
    enableAuth: document.getElementById('enableAuth').checked,
    cacheTimeout: parseInt(document.getElementById('cacheTimeout').value),
    logLevel: document.getElementById('logLevel').value
  };

  try {
    console.log('Sauvegarde configuration système:', config);
    showSuccess('Configuration système sauvegardée');
  } catch (error) {
    console.error('Erreur sauvegarde système:', error);
    showError('Erreur lors de la sauvegarde de la configuration système');
  }
}

// Tester la connectivité réseau
async function testNetworkConnectivity() {
  showInfo('Test de connectivité en cours...');

  try {
    // Simuler un test de connectivité
    await new Promise(resolve => setTimeout(resolve, 2000));

    const results = {
      local: true,
      network: true,
      api: true
    };

    if (results.local && results.network && results.api) {
      showSuccess('Test de connectivité réussi ! Tous les services sont accessibles.');
    } else {
      showError('Problèmes de connectivité détectés. Vérifiez la configuration.');
    }
  } catch (error) {
    console.error('Erreur test connectivité:', error);
    showError('Erreur lors du test de connectivité');
  }
}

// Redémarrer le serveur
async function restartServer() {
  const confirmed = confirm('Redémarrer le serveur ? Tous les utilisateurs connectés seront déconnectés.');
  if (!confirmed) return;

  try {
    showInfo('Redémarrage du serveur en cours...');
    console.log('Redémarrage serveur demandé');

    // Dans un vrai système, on enverrait une requête au serveur
    setTimeout(() => {
      showSuccess('Serveur redémarré avec succès');
    }, 3000);

  } catch (error) {
    console.error('Erreur redémarrage:', error);
    showError('Erreur lors du redémarrage du serveur');
  }
}

// Actualiser le monitoring
async function refreshMonitoring() {
  try {
    showInfo('Actualisation des données de monitoring...');

    // Simuler le chargement des utilisateurs connectés
    const connectedUsers = [
      { username: 'admin', ip: '*************', since: '10:30' },
      { username: 'manager1', ip: '*************', since: '09:15' }
    ];

    const connectedList = document.getElementById('connectedUsersList');
    connectedList.innerHTML = connectedUsers.map(user => `
      <div class="list-group-item">
        <div class="d-flex justify-content-between">
          <strong>${user.username}</strong>
          <small>${user.since}</small>
        </div>
        <small class="text-muted">${user.ip}</small>
      </div>
    `).join('');

    // Simuler l'activité récente
    const recentActivity = [
      'Connexion utilisateur: manager1',
      'Création facture: FACT-004',
      'Modification produit: ID 15',
      'Sauvegarde automatique effectuée'
    ];

    const activityList = document.getElementById('recentActivityList');
    activityList.innerHTML = recentActivity.map(activity => `
      <div class="list-group-item">
        <small>${activity}</small>
      </div>
    `).join('');

    // Simuler les logs système
    const logs = `[${new Date().toLocaleTimeString()}] INFO: Monitoring actualisé
[${new Date().toLocaleTimeString()}] INFO: 2 utilisateurs connectés
[${new Date().toLocaleTimeString()}] INFO: Base de données: OK
[${new Date().toLocaleTimeString()}] INFO: Mémoire utilisée: 45%`;

    document.getElementById('systemLogs').textContent = logs;

    showSuccess('Données de monitoring actualisées');
  } catch (error) {
    console.error('Erreur actualisation monitoring:', error);
    showError('Erreur lors de l\'actualisation du monitoring');
  }
}

// Créer une sauvegarde
async function createBackup() {
  try {
    showInfo('Création de la sauvegarde en cours...');

    // Simuler la création d'une sauvegarde
    await new Promise(resolve => setTimeout(resolve, 3000));

    const backupName = `backup_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.sql`;
    console.log('Sauvegarde créée:', backupName);

    showSuccess(`Sauvegarde créée avec succès: ${backupName}`);
    loadBackupsList();
  } catch (error) {
    console.error('Erreur création sauvegarde:', error);
    showError('Erreur lors de la création de la sauvegarde');
  }
}

// Restaurer une sauvegarde
async function restoreBackup() {
  const fileInput = document.getElementById('backupFile');
  if (!fileInput.files.length) {
    showError('Veuillez sélectionner un fichier de sauvegarde');
    return;
  }

  const confirmed = confirm('Restaurer la sauvegarde ? Toutes les données actuelles seront remplacées !');
  if (!confirmed) return;

  try {
    showInfo('Restauration en cours...');

    // Simuler la restauration
    await new Promise(resolve => setTimeout(resolve, 5000));

    showSuccess('Sauvegarde restaurée avec succès');
  } catch (error) {
    console.error('Erreur restauration:', error);
    showError('Erreur lors de la restauration de la sauvegarde');
  }
}

// Charger la liste des sauvegardes
function loadBackupsList() {
  const backups = [
    { name: 'backup_2024-05-26_10-30-00.sql', size: '2.5 MB', date: '2024-05-26 10:30:00' },
    { name: 'backup_2024-05-25_18-00-00.sql', size: '2.3 MB', date: '2024-05-25 18:00:00' },
    { name: 'backup_2024-05-24_12-00-00.sql', size: '2.1 MB', date: '2024-05-24 12:00:00' }
  ];

  const tableBody = document.getElementById('backupsTableBody');
  tableBody.innerHTML = backups.map(backup => `
    <tr>
      <td>${new Date(backup.date).toLocaleString()}</td>
      <td>${backup.size}</td>
      <td>
        <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('${backup.name}')">
          <i class="bi bi-download"></i> Télécharger
        </button>
        <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('${backup.name}')">
          <i class="bi bi-trash"></i> Supprimer
        </button>
      </td>
    </tr>
  `).join('');
}

// Télécharger une sauvegarde
function downloadBackup(filename) {
  console.log('Téléchargement sauvegarde:', filename);
  showInfo(`Téléchargement de ${filename}...`);
}

// Supprimer une sauvegarde
function deleteBackup(filename) {
  const confirmed = confirm(`Supprimer la sauvegarde ${filename} ?`);
  if (!confirmed) return;

  console.log('Suppression sauvegarde:', filename);
  showSuccess('Sauvegarde supprimée');
  loadBackupsList();
}

// Copier dans le presse-papiers
function copyToClipboard(elementId) {
  const element = document.getElementById(elementId);
  const text = element.textContent;

  navigator.clipboard.writeText(text).then(() => {
    showSuccess('URL copiée dans le presse-papiers');
  }).catch(() => {
    showError('Erreur lors de la copie');
  });
}

// Fonctions utilitaires
function showSuccess(message) {
  if (typeof window.showSuccess === 'function') {
    window.showSuccess(message);
  } else {
    console.log('✅ ' + message);
  }
}

function showError(message) {
  if (typeof window.showError === 'function') {
    window.showError(message);
  } else {
    console.error('❌ ' + message);
  }
}

function showInfo(message) {
  if (typeof window.showInfo === 'function') {
    window.showInfo(message);
  } else {
    console.log('ℹ️ ' + message);
  }
}
