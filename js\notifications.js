// Système de notifications global
class NotificationManager {
  constructor() {
    this.container = document.getElementById('notification-container');
    this.notifications = [];
  }

  // Afficher une notification de succès (verte)
  showSuccess(message, duration = 4000) {
    this.show(message, 'success', duration);
  }

  // Afficher une notification d'erreur (rouge)
  showError(message, duration = 6000) {
    this.show(message, 'danger', duration);
  }

  // Afficher une notification d'information (bleue)
  showInfo(message, duration = 4000) {
    this.show(message, 'info', duration);
  }

  // Afficher une notification d'avertissement (jaune)
  showWarning(message, duration = 5000) {
    this.show(message, 'warning', duration);
  }

  // Méthode principale pour afficher une notification
  show(message, type = 'info', duration = 4000) {
    const id = 'notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.id = id;
    notification.className = `alert alert-${type} alert-dismissible fade show mb-2`;
    notification.setAttribute('role', 'alert');
    notification.style.minWidth = '300px';
    notification.style.maxWidth = '400px';

    // Icône selon le type
    let icon = '';
    switch (type) {
      case 'success':
        icon = '<i class="bi bi-check-circle-fill me-2"></i>';
        break;
      case 'danger':
        icon = '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
        break;
      case 'warning':
        icon = '<i class="bi bi-exclamation-circle-fill me-2"></i>';
        break;
      case 'info':
        icon = '<i class="bi bi-info-circle-fill me-2"></i>';
        break;
    }

    notification.innerHTML = `
      ${icon}${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Ajouter au container
    this.container.appendChild(notification);
    this.notifications.push({ id, element: notification });

    // Animation d'entrée
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // Suppression automatique après la durée spécifiée
    if (duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, duration);
    }

    // Gestionnaire pour le bouton de fermeture
    const closeBtn = notification.querySelector('.btn-close');
    closeBtn.addEventListener('click', () => {
      this.remove(id);
    });

    return id;
  }

  // Supprimer une notification
  remove(id) {
    const notification = this.notifications.find(n => n.id === id);
    if (notification) {
      // Animation de sortie
      notification.element.classList.remove('show');
      notification.element.classList.add('fade');

      setTimeout(() => {
        if (notification.element.parentNode) {
          notification.element.parentNode.removeChild(notification.element);
        }
        this.notifications = this.notifications.filter(n => n.id !== id);
      }, 150);
    }
  }

  // Supprimer toutes les notifications
  clear() {
    this.notifications.forEach(notification => {
      this.remove(notification.id);
    });
  }
}

// Instance globale du gestionnaire de notifications
const notificationManager = new NotificationManager();

// Fonctions globales pour faciliter l'utilisation
window.showSuccess = (message, duration) => notificationManager.showSuccess(message, duration);
window.showError = (message, duration) => notificationManager.showError(message, duration);
window.showInfo = (message, duration) => notificationManager.showInfo(message, duration);
window.showWarning = (message, duration) => notificationManager.showWarning(message, duration);

// Fonction pour remplacer les alert() existants
window.showAlert = (message, type = 'info') => {
  switch (type) {
    case 'success':
      return notificationManager.showSuccess(message);
    case 'error':
    case 'danger':
      return notificationManager.showError(message);
    case 'warning':
      return notificationManager.showWarning(message);
    default:
      return notificationManager.showInfo(message);
  }
};

// Fonction pour créer un modal de confirmation élégant
window.showConfirm = (message, title = 'Confirmation', options = {}) => {
  return new Promise((resolve) => {
    // Créer le modal de confirmation
    const modalId = 'confirm-modal-' + Date.now();
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-hidden', 'true');

    modal.innerHTML = `
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-question-circle-fill text-warning me-2"></i>
              ${title}
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p class="mb-0">${message}</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              ${options.cancelText || 'Annuler'}
            </button>
            <button type="button" class="btn btn-danger" id="confirm-btn">
              ${options.confirmText || 'Confirmer'}
            </button>
          </div>
        </div>
      </div>
    `;

    // Ajouter au DOM
    document.body.appendChild(modal);

    // Créer l'instance Bootstrap Modal
    const bsModal = new bootstrap.Modal(modal);

    // Gestionnaires d'événements
    const confirmBtn = modal.querySelector('#confirm-btn');
    const cancelBtns = modal.querySelectorAll('[data-bs-dismiss="modal"]');

    confirmBtn.addEventListener('click', () => {
      bsModal.hide();
      resolve(true);
    });

    cancelBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        resolve(false);
      });
    });

    // Nettoyer le DOM quand le modal est fermé
    modal.addEventListener('hidden.bs.modal', () => {
      document.body.removeChild(modal);
    });

    // Afficher le modal
    bsModal.show();
  });
};
