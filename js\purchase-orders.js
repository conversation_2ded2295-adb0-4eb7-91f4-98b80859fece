// Gestionnaire pour les bons de commande
class PurchaseOrdersManager {
  constructor() {
    this.suppliers = [];
    this.products = [];
    this.purchaseOrders = [];
    this.init();
  }

  async init() {
    console.log('🛒 Initialisation du gestionnaire de bons de commande...');

    // Charger les données
    await this.loadSuppliers();
    await this.loadProducts();
    await this.loadPurchaseOrders();

    // Initialiser les événements
    this.initEventListeners();

    console.log('✅ Gestionnaire de bons de commande initialisé');
  }

  async loadSuppliers() {
    try {
      console.log('📦 Chargement des fournisseurs...');
      const response = await window.APP_CONFIG.fetch('/suppliers');

      if (response.ok) {
        this.suppliers = await response.json();
        console.log(`✅ ${this.suppliers.length} fournisseurs chargés`);
        this.populateSupplierSelect();
      } else {
        console.error('❌ Erreur lors du chargement des fournisseurs:', response.status);
        this.showError('Erreur lors du chargement des fournisseurs');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des fournisseurs:', error);
      this.showError('Impossible de charger les fournisseurs');
    }
  }

  async loadProducts() {
    try {
      console.log('📦 Chargement des produits...');
      const response = await window.APP_CONFIG.fetch('/products');

      if (response.ok) {
        this.products = await response.json();
        console.log(`✅ ${this.products.length} produits chargés`);
        this.populateProductSelects();
      } else {
        console.error('❌ Erreur lors du chargement des produits:', response.status);
        this.showError('Erreur lors du chargement des produits');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des produits:', error);
      this.showError('Impossible de charger les produits');
    }
  }

  async loadPurchaseOrders() {
    try {
      console.log('📦 Chargement des bons de commande...');
      const response = await window.APP_CONFIG.fetch('/purchase-orders');

      if (response.ok) {
        this.purchaseOrders = await response.json();
        console.log(`✅ ${this.purchaseOrders.length} bons de commande chargés`);
        this.displayPurchaseOrders();
      } else {
        console.error('❌ Erreur lors du chargement des bons de commande:', response.status);
        this.showError('Erreur lors du chargement des bons de commande');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des bons de commande:', error);
      this.showError('Impossible de charger les bons de commande');
    }
  }

  populateSupplierSelect() {
    const supplierSelect = document.getElementById('purchaseOrderSupplier');
    if (!supplierSelect) return;

    // Vider les options existantes (sauf la première)
    supplierSelect.innerHTML = '<option value="">Sélectionner un fournisseur</option>';

    // Ajouter les fournisseurs
    this.suppliers.forEach(supplier => {
      const option = document.createElement('option');
      option.value = supplier.id;
      option.textContent = supplier.name;
      supplierSelect.appendChild(option);
      console.log(`📦 Fournisseur ajouté: ID=${supplier.id}, Nom=${supplier.name}`);
    });

    console.log(`✅ ${this.suppliers.length} fournisseurs ajoutés au select`);
  }

  populateProductSelects() {
    const productSelects = document.querySelectorAll('.product-select');

    productSelects.forEach(select => {
      // Vider les options existantes (sauf la première)
      select.innerHTML = '<option value="">Sélectionner un produit</option>';

      // Ajouter les produits
      this.products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = `${product.name} - ${product.price.toFixed(2)} DA`;
        option.dataset.price = product.price;
        select.appendChild(option);
      });
    });

    console.log(`✅ ${this.products.length} produits ajoutés aux selects`);
  }

  displayPurchaseOrders() {
    const tbody = document.getElementById('purchaseOrdersTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    this.purchaseOrders.forEach(order => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${order.number}</td>
        <td>${new Date(order.date).toLocaleDateString('fr-FR')}</td>
        <td>${order.supplier}</td>
        <td>${order.amount.toFixed(2)} DA</td>
        <td>
          <span class="badge ${this.getStatusBadgeClass(order.status)}">${order.status}</span>
        </td>
        <td>
          <button class="btn btn-sm btn-outline-primary me-1" onclick="purchaseOrdersManager.viewOrder(${order.id})" title="Voir">
            <i class="bi bi-eye"></i>
          </button>
          <button class="btn btn-sm btn-outline-warning me-1" onclick="purchaseOrdersManager.editOrder(${order.id})" title="Modifier">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger" onclick="purchaseOrdersManager.deleteOrder(${order.id})" title="Supprimer">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      `;
      tbody.appendChild(row);
    });
  }

  getStatusBadgeClass(status) {
    switch (status) {
      case 'En attente': return 'bg-warning';
      case 'Confirmé': return 'bg-success';
      case 'Livré': return 'bg-primary';
      case 'Annulé': return 'bg-danger';
      default: return 'bg-secondary';
    }
  }

  initEventListeners() {
    // Bouton ajouter un produit
    const addItemBtn = document.getElementById('addPurchaseOrderItemBtn');
    if (addItemBtn) {
      addItemBtn.addEventListener('click', () => this.addProductLine());
    }

    // Bouton sauvegarder
    const saveBtn = document.getElementById('savePurchaseOrderBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => this.savePurchaseOrder());
    }

    // Gestion des changements de produit et quantité
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('product-select') ||
          e.target.classList.contains('product-quantity') ||
          e.target.classList.contains('product-price')) {
        this.updateLineTotal(e.target.closest('.purchase-order-item'));
        this.updateOrderTotal();
      }
    });

    // Gestion des boutons supprimer
    document.addEventListener('click', (e) => {
      if (e.target.closest('.remove-item')) {
        this.removeProductLine(e.target.closest('.purchase-order-item'));
      }
    });

    // Recherche
    const searchInput = document.getElementById('searchPurchaseOrder');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => this.filterPurchaseOrders(e.target.value));
    }

    // Initialiser la date d'aujourd'hui
    const dateInput = document.getElementById('purchaseOrderDate');
    if (dateInput) {
      dateInput.value = new Date().toISOString().split('T')[0];
    }
  }

  addProductLine() {
    const container = document.getElementById('purchaseOrderItems');
    const newLine = document.createElement('div');
    newLine.className = 'row mb-2 purchase-order-item';
    newLine.innerHTML = `
      <div class="col-md-5">
        <select class="form-select product-select" required>
          <option value="">Sélectionner un produit</option>
        </select>
      </div>
      <div class="col-md-2">
        <input type="number" class="form-control product-quantity" placeholder="Qté" min="1" required>
      </div>
      <div class="col-md-2">
        <input type="number" step="0.01" class="form-control product-price" placeholder="Prix" required>
      </div>
      <div class="col-md-2">
        <input type="text" class="form-control product-total" placeholder="Total" readonly>
      </div>
      <div class="col-md-1">
        <button type="button" class="btn btn-outline-danger remove-item">
          <i class="bi bi-trash"></i>
        </button>
      </div>
    `;

    container.appendChild(newLine);

    // Peupler le nouveau select avec les produits
    const newSelect = newLine.querySelector('.product-select');
    this.products.forEach(product => {
      const option = document.createElement('option');
      option.value = product.id;
      option.textContent = `${product.name} - ${product.price.toFixed(2)} DA`;
      option.dataset.price = product.price;
      newSelect.appendChild(option);
    });
  }

  removeProductLine(line) {
    if (document.querySelectorAll('.purchase-order-item').length > 1) {
      line.remove();
      this.updateOrderTotal();
    } else {
      this.showError('Au moins un produit est requis');
    }
  }

  updateLineTotal(line) {
    const select = line.querySelector('.product-select');
    const quantityInput = line.querySelector('.product-quantity');
    const priceInput = line.querySelector('.product-price');
    const totalInput = line.querySelector('.product-total');

    const quantity = parseFloat(quantityInput.value) || 0;
    let price = parseFloat(priceInput.value) || 0;

    // Si un produit est sélectionné et le prix n'est pas renseigné, utiliser le prix du produit
    if (select.value && !priceInput.value) {
      const selectedOption = select.querySelector(`option[value="${select.value}"]`);
      if (selectedOption && selectedOption.dataset.price) {
        price = parseFloat(selectedOption.dataset.price);
        priceInput.value = price.toFixed(2);
      }
    }

    const total = quantity * price;
    totalInput.value = total.toFixed(2);
  }

  updateOrderTotal() {
    const lines = document.querySelectorAll('.purchase-order-item');
    let subtotal = 0;

    lines.forEach(line => {
      const totalInput = line.querySelector('.product-total');
      const total = parseFloat(totalInput.value) || 0;
      subtotal += total;
    });

    const tax = subtotal * 0.19; // TVA 19%
    const total = subtotal + tax;

    document.getElementById('purchaseOrderSubtotal').value = subtotal.toFixed(2);
    document.getElementById('purchaseOrderTax').value = tax.toFixed(2);
    document.getElementById('purchaseOrderTotal').value = total.toFixed(2);
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showNotification(message, type = 'info') {
    // Créer une notification Bootstrap
    const alertClass = type === 'error' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(notification);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  // Méthodes pour les actions
  viewOrder(id) {
    console.log('Voir bon de commande:', id);
    // TODO: Implémenter la vue détaillée
  }

  editOrder(id) {
    console.log('Modifier bon de commande:', id);
    // TODO: Implémenter la modification
  }

  deleteOrder(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce bon de commande ?')) {
      console.log('Supprimer bon de commande:', id);
      // TODO: Implémenter la suppression
    }
  }

  async savePurchaseOrder() {
    try {
      console.log('💾 Sauvegarde du bon de commande...');

      // Récupérer les données du formulaire
      const formData = this.collectFormData();
      console.log('📋 Données collectées:', formData);

      // Valider les données
      if (!this.validateFormData(formData)) {
        return;
      }

      console.log('✅ Validation réussie, envoi au serveur...');

      // Envoyer les données au serveur
      const response = await window.APP_CONFIG.fetch('/purchase-orders', {
        method: 'POST',
        body: JSON.stringify(formData)
      });

      console.log('📡 Réponse du serveur:', response.status, response.statusText);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Bon de commande sauvegardé:', result);

        this.showSuccess('Bon de commande sauvegardé avec succès !');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addPurchaseOrderModal'));
        if (modal) {
          modal.hide();
        }

        // Recharger la liste
        await this.loadPurchaseOrders();

        // Réinitialiser le formulaire
        this.resetForm();

      } else {
        let errorMessage = 'Erreur lors de la sauvegarde';
        try {
          const error = await response.json();
          console.error('❌ Erreur lors de la sauvegarde:', error);
          errorMessage = error.error || error.message || errorMessage;
        } catch (parseError) {
          console.error('❌ Erreur lors du parsing de la réponse d\'erreur:', parseError);
          const errorText = await response.text();
          console.error('❌ Texte de la réponse:', errorText);
          errorMessage = `Erreur ${response.status}: ${response.statusText}`;
        }
        this.showError(errorMessage);
      }

    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error);
      this.showError('Erreur de connexion lors de la sauvegarde');
    }
  }

  collectFormData() {
    const date = document.getElementById('purchaseOrderDate').value;
    const supplierId = document.getElementById('purchaseOrderSupplier').value;
    const notes = document.getElementById('purchaseOrderNotes').value;

    // Récupérer les lignes de produits
    const items = [];
    const itemRows = document.querySelectorAll('.purchase-order-item');

    itemRows.forEach(row => {
      const productId = row.querySelector('.product-select').value;
      const quantity = parseFloat(row.querySelector('.product-quantity').value) || 0;
      const price = parseFloat(row.querySelector('.product-price').value) || 0;
      const total = parseFloat(row.querySelector('.product-total').value) || 0;

      if (productId && quantity > 0 && price > 0) {
        items.push({
          productId: parseInt(productId),
          quantity: quantity,
          unitPrice: price,
          total: total
        });
      }
    });

    const subtotal = parseFloat(document.getElementById('purchaseOrderSubtotal').value) || 0;
    const tax = parseFloat(document.getElementById('purchaseOrderTax').value) || 0;
    const total = parseFloat(document.getElementById('purchaseOrderTotal').value) || 0;

    return {
      date,
      supplierId: parseInt(supplierId),
      items,
      subtotal,
      taxRate: 19, // TVA 19%
      taxAmount: tax,
      total,
      notes,
      status: 'En attente'
    };
  }

  validateFormData(data) {
    if (!data.date) {
      this.showError('La date est requise');
      return false;
    }

    if (!data.supplierId) {
      this.showError('Le fournisseur est requis');
      return false;
    }

    if (!data.items || data.items.length === 0) {
      this.showError('Au moins un produit est requis');
      return false;
    }

    if (data.total <= 0) {
      this.showError('Le montant total doit être supérieur à 0');
      return false;
    }

    return true;
  }

  resetForm() {
    // Réinitialiser le formulaire
    document.getElementById('addPurchaseOrderForm').reset();

    // Remettre la date d'aujourd'hui
    document.getElementById('purchaseOrderDate').value = new Date().toISOString().split('T')[0];

    // Garder seulement la première ligne de produit
    const container = document.getElementById('purchaseOrderItems');
    const firstItem = container.querySelector('.purchase-order-item');
    container.innerHTML = '';
    container.appendChild(firstItem);

    // Réinitialiser la première ligne
    firstItem.querySelector('.product-select').value = '';
    firstItem.querySelector('.product-quantity').value = '';
    firstItem.querySelector('.product-price').value = '';
    firstItem.querySelector('.product-total').value = '';

    // Réinitialiser les totaux
    document.getElementById('purchaseOrderSubtotal').value = '';
    document.getElementById('purchaseOrderTax').value = '';
    document.getElementById('purchaseOrderTotal').value = '';
  }

  filterPurchaseOrders(searchTerm) {
    // TODO: Implémenter la recherche
    console.log('Rechercher:', searchTerm);
  }
}

// Initialiser le gestionnaire
let purchaseOrdersManager;

// Fonction pour initialiser le gestionnaire quand la section est activée
function initPurchaseOrdersManager() {
  if (!purchaseOrdersManager) {
    console.log('🛒 Initialisation du gestionnaire de bons de commande...');
    purchaseOrdersManager = new PurchaseOrdersManager();
    window.purchaseOrdersManager = purchaseOrdersManager;
  }
}

// Écouter l'événement de changement de section
document.addEventListener('sectionChanged', function(event) {
  if (event.detail.section === 'purchase-orders') {
    console.log('📄 Section bons de commande activée');
    initPurchaseOrdersManager();
  }
});

// Initialiser aussi au chargement de la page si la section est déjà active
document.addEventListener('DOMContentLoaded', function() {
  // Vérifier si la section bons de commande est déjà active
  setTimeout(() => {
    const activeSection = document.querySelector('.section-content[style*="block"]');
    if (activeSection && activeSection.querySelector('#addPurchaseOrderModal')) {
      console.log('📄 Section bons de commande déjà active au chargement');
      initPurchaseOrdersManager();
    }
  }, 1000);
});

// Exporter pour utilisation globale
window.initPurchaseOrdersManager = initPurchaseOrdersManager;
