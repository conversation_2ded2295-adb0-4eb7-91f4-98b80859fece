// Gestionnaire pour les bons de commande
class PurchaseOrdersManager {
  constructor() {
    this.suppliers = [];
    this.products = [];
    this.purchaseOrders = [];
    this.init();
  }

  async init() {
    console.log('🛒 Initialisation du gestionnaire de bons de commande...');

    // Charger les données
    await this.loadSuppliers();
    await this.loadProducts();
    await this.loadPurchaseOrders();

    // Initialiser les événements
    this.initEventListeners();

    console.log('✅ Gestionnaire de bons de commande initialisé');
  }

  async loadSuppliers() {
    try {
      console.log('📦 Chargement des fournisseurs...');
      const response = await window.APP_CONFIG.fetch('/suppliers');

      if (response.ok) {
        this.suppliers = await response.json();
        console.log(`✅ ${this.suppliers.length} fournisseurs chargés`);
        this.populateSupplierSelect();
      } else {
        console.error('❌ Erreur lors du chargement des fournisseurs:', response.status);
        this.showError('Erreur lors du chargement des fournisseurs');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des fournisseurs:', error);
      this.showError('Impossible de charger les fournisseurs');
    }
  }

  async loadProducts() {
    try {
      console.log('📦 Chargement des produits...');
      const response = await window.APP_CONFIG.fetch('/products');

      if (response.ok) {
        this.products = await response.json();
        console.log(`✅ ${this.products.length} produits chargés`);
        this.populateProductSelects();
      } else {
        console.error('❌ Erreur lors du chargement des produits:', response.status);
        this.showError('Erreur lors du chargement des produits');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des produits:', error);
      this.showError('Impossible de charger les produits');
    }
  }

  async loadPurchaseOrders() {
    try {
      console.log('📦 Chargement des bons de commande...');
      // Pour l'instant, utiliser des données de test
      this.purchaseOrders = [
        {
          id: 1,
          number: 'BC-2024-001',
          date: '2024-01-15',
          supplier: 'Tech Supplies Inc.',
          amount: 15000.00,
          status: 'En attente'
        },
        {
          id: 2,
          number: 'BC-2024-002',
          date: '2024-01-16',
          supplier: 'Global Electronics',
          amount: 25000.00,
          status: 'Confirmé'
        }
      ];

      console.log(`✅ ${this.purchaseOrders.length} bons de commande chargés`);
      this.displayPurchaseOrders();
    } catch (error) {
      console.error('❌ Erreur lors du chargement des bons de commande:', error);
      this.showError('Impossible de charger les bons de commande');
    }
  }

  populateSupplierSelect() {
    const supplierSelect = document.getElementById('purchaseOrderSupplier');
    if (!supplierSelect) return;

    // Vider les options existantes (sauf la première)
    supplierSelect.innerHTML = '<option value="">Sélectionner un fournisseur</option>';

    // Ajouter les fournisseurs
    this.suppliers.forEach(supplier => {
      const option = document.createElement('option');
      option.value = supplier.id;
      option.textContent = supplier.name;
      supplierSelect.appendChild(option);
    });

    console.log(`✅ ${this.suppliers.length} fournisseurs ajoutés au select`);
  }

  populateProductSelects() {
    const productSelects = document.querySelectorAll('.product-select');

    productSelects.forEach(select => {
      // Vider les options existantes (sauf la première)
      select.innerHTML = '<option value="">Sélectionner un produit</option>';

      // Ajouter les produits
      this.products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = `${product.name} - ${product.price.toFixed(2)} DA`;
        option.dataset.price = product.price;
        select.appendChild(option);
      });
    });

    console.log(`✅ ${this.products.length} produits ajoutés aux selects`);
  }

  displayPurchaseOrders() {
    const tbody = document.getElementById('purchaseOrdersTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    this.purchaseOrders.forEach(order => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${order.number}</td>
        <td>${new Date(order.date).toLocaleDateString('fr-FR')}</td>
        <td>${order.supplier}</td>
        <td>${order.amount.toFixed(2)} DA</td>
        <td>
          <span class="badge ${this.getStatusBadgeClass(order.status)}">${order.status}</span>
        </td>
        <td>
          <button class="btn btn-sm btn-outline-primary me-1" onclick="purchaseOrdersManager.viewOrder(${order.id})" title="Voir">
            <i class="bi bi-eye"></i>
          </button>
          <button class="btn btn-sm btn-outline-warning me-1" onclick="purchaseOrdersManager.editOrder(${order.id})" title="Modifier">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger" onclick="purchaseOrdersManager.deleteOrder(${order.id})" title="Supprimer">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      `;
      tbody.appendChild(row);
    });
  }

  getStatusBadgeClass(status) {
    switch (status) {
      case 'En attente': return 'bg-warning';
      case 'Confirmé': return 'bg-success';
      case 'Livré': return 'bg-primary';
      case 'Annulé': return 'bg-danger';
      default: return 'bg-secondary';
    }
  }

  initEventListeners() {
    // Bouton ajouter un produit
    const addItemBtn = document.getElementById('addPurchaseOrderItemBtn');
    if (addItemBtn) {
      addItemBtn.addEventListener('click', () => this.addProductLine());
    }

    // Bouton sauvegarder
    const saveBtn = document.getElementById('savePurchaseOrderBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => this.savePurchaseOrder());
    }

    // Gestion des changements de produit et quantité
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('product-select') ||
          e.target.classList.contains('product-quantity') ||
          e.target.classList.contains('product-price')) {
        this.updateLineTotal(e.target.closest('.purchase-order-item'));
        this.updateOrderTotal();
      }
    });

    // Gestion des boutons supprimer
    document.addEventListener('click', (e) => {
      if (e.target.closest('.remove-item')) {
        this.removeProductLine(e.target.closest('.purchase-order-item'));
      }
    });

    // Recherche
    const searchInput = document.getElementById('searchPurchaseOrder');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => this.filterPurchaseOrders(e.target.value));
    }

    // Initialiser la date d'aujourd'hui
    const dateInput = document.getElementById('purchaseOrderDate');
    if (dateInput) {
      dateInput.value = new Date().toISOString().split('T')[0];
    }
  }

  addProductLine() {
    const container = document.getElementById('purchaseOrderItems');
    const newLine = document.createElement('div');
    newLine.className = 'row mb-2 purchase-order-item';
    newLine.innerHTML = `
      <div class="col-md-5">
        <select class="form-select product-select" required>
          <option value="">Sélectionner un produit</option>
        </select>
      </div>
      <div class="col-md-2">
        <input type="number" class="form-control product-quantity" placeholder="Qté" min="1" required>
      </div>
      <div class="col-md-2">
        <input type="number" step="0.01" class="form-control product-price" placeholder="Prix" required>
      </div>
      <div class="col-md-2">
        <input type="text" class="form-control product-total" placeholder="Total" readonly>
      </div>
      <div class="col-md-1">
        <button type="button" class="btn btn-outline-danger remove-item">
          <i class="bi bi-trash"></i>
        </button>
      </div>
    `;

    container.appendChild(newLine);

    // Peupler le nouveau select avec les produits
    const newSelect = newLine.querySelector('.product-select');
    this.products.forEach(product => {
      const option = document.createElement('option');
      option.value = product.id;
      option.textContent = `${product.name} - ${product.price.toFixed(2)} DA`;
      option.dataset.price = product.price;
      newSelect.appendChild(option);
    });
  }

  removeProductLine(line) {
    if (document.querySelectorAll('.purchase-order-item').length > 1) {
      line.remove();
      this.updateOrderTotal();
    } else {
      this.showError('Au moins un produit est requis');
    }
  }

  updateLineTotal(line) {
    const select = line.querySelector('.product-select');
    const quantityInput = line.querySelector('.product-quantity');
    const priceInput = line.querySelector('.product-price');
    const totalInput = line.querySelector('.product-total');

    const quantity = parseFloat(quantityInput.value) || 0;
    let price = parseFloat(priceInput.value) || 0;

    // Si un produit est sélectionné et le prix n'est pas renseigné, utiliser le prix du produit
    if (select.value && !priceInput.value) {
      const selectedOption = select.querySelector(`option[value="${select.value}"]`);
      if (selectedOption && selectedOption.dataset.price) {
        price = parseFloat(selectedOption.dataset.price);
        priceInput.value = price.toFixed(2);
      }
    }

    const total = quantity * price;
    totalInput.value = total.toFixed(2);
  }

  updateOrderTotal() {
    const lines = document.querySelectorAll('.purchase-order-item');
    let subtotal = 0;

    lines.forEach(line => {
      const totalInput = line.querySelector('.product-total');
      const total = parseFloat(totalInput.value) || 0;
      subtotal += total;
    });

    const tax = subtotal * 0.19; // TVA 19%
    const total = subtotal + tax;

    document.getElementById('purchaseOrderSubtotal').value = subtotal.toFixed(2);
    document.getElementById('purchaseOrderTax').value = tax.toFixed(2);
    document.getElementById('purchaseOrderTotal').value = total.toFixed(2);
  }

  showError(message) {
    // Utiliser le système de notification global s'il existe
    if (window.showNotification) {
      window.showNotification(message, 'error');
    } else {
      alert(message);
    }
  }

  showSuccess(message) {
    if (window.showNotification) {
      window.showNotification(message, 'success');
    } else {
      alert(message);
    }
  }

  // Méthodes pour les actions
  viewOrder(id) {
    console.log('Voir bon de commande:', id);
    // TODO: Implémenter la vue détaillée
  }

  editOrder(id) {
    console.log('Modifier bon de commande:', id);
    // TODO: Implémenter la modification
  }

  deleteOrder(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce bon de commande ?')) {
      console.log('Supprimer bon de commande:', id);
      // TODO: Implémenter la suppression
    }
  }

  savePurchaseOrder() {
    console.log('Sauvegarder bon de commande');
    // TODO: Implémenter la sauvegarde
    this.showSuccess('Bon de commande sauvegardé avec succès');
  }

  filterPurchaseOrders(searchTerm) {
    // TODO: Implémenter la recherche
    console.log('Rechercher:', searchTerm);
  }
}

// Initialiser le gestionnaire
let purchaseOrdersManager;

// Fonction pour initialiser le gestionnaire quand la section est activée
function initPurchaseOrdersManager() {
  if (!purchaseOrdersManager) {
    console.log('🛒 Initialisation du gestionnaire de bons de commande...');
    purchaseOrdersManager = new PurchaseOrdersManager();
    window.purchaseOrdersManager = purchaseOrdersManager;
  }
}

// Écouter l'événement de changement de section
document.addEventListener('sectionChanged', function(event) {
  if (event.detail.section === 'purchase-orders') {
    console.log('📄 Section bons de commande activée');
    initPurchaseOrdersManager();
  }
});

// Initialiser aussi au chargement de la page si la section est déjà active
document.addEventListener('DOMContentLoaded', function() {
  // Vérifier si la section bons de commande est déjà active
  setTimeout(() => {
    const activeSection = document.querySelector('.section-content[style*="block"]');
    if (activeSection && activeSection.querySelector('#addPurchaseOrderModal')) {
      console.log('📄 Section bons de commande déjà active au chargement');
      initPurchaseOrdersManager();
    }
  }, 1000);
});

// Exporter pour utilisation globale
window.initPurchaseOrdersManager = initPurchaseOrdersManager;
