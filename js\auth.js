// Objet pour gérer l'authentification
const auth = {
  // Vérifier si l'utilisateur est connecté
  isLoggedIn: function() {
    return localStorage.getItem('authToken') !== null;
  },

  // Connecter l'utilisateur avec l'API
  login: async function(username, password) {
    try {
      console.log('🔐 Tentative de connexion pour:', username);

      // Utiliser la configuration réseau (sans le préfixe /api pour l'auth)
      const authUrl = window.APP_CONFIG.BASE_URL + '/auth/login';
      console.log('🔗 URL d\'authentification:', authUrl);

      const response = await fetch(authUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Connexion réussie:', data);

        // Stocker les informations de session
        localStorage.setItem('authToken', data.token || 'authenticated');
        localStorage.setItem('username', data.username || username);
        localStorage.setItem('userRole', data.role || 'user');
        localStorage.setItem('userId', data.id || '1');

        return { success: true, user: data };
      } else {
        const errorData = await response.json();
        console.error('❌ Erreur de connexion:', errorData);
        return { success: false, error: errorData.error || 'Identifiants incorrects' };
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);

      // Fallback pour l'authentification locale
      console.log('🔄 Tentative d\'authentification locale...');
      if (username === 'admin' && password === 'admin') {
        const token = 'local-admin-token-' + Math.random().toString(36).substring(2);
        localStorage.setItem('authToken', token);
        localStorage.setItem('username', username);
        localStorage.setItem('userRole', 'admin');
        localStorage.setItem('userId', '1');
        return { success: true, user: { username, role: 'admin' } };
      }

      return { success: false, error: 'Impossible de se connecter au serveur. Vérifiez votre connexion réseau.' };
    }
  },

  // Déconnecter l'utilisateur
  logout: function() {
    console.log('🚪 Déconnexion...');
    localStorage.removeItem('authToken');
    localStorage.removeItem('username');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userId');
    window.location.href = 'login.html';
  },

  // Obtenir le nom d'utilisateur
  getUsername: function() {
    return localStorage.getItem('username') || 'Utilisateur';
  },

  // Obtenir le rôle de l'utilisateur
  getUserRole: function() {
    return localStorage.getItem('userRole') || 'user';
  },

  // Obtenir l'ID de l'utilisateur
  getUserId: function() {
    return localStorage.getItem('userId') || '1';
  },

  // Vérifier si l'utilisateur a un rôle spécifique
  hasRole: function(role) {
    const userRole = this.getUserRole();
    const roleHierarchy = {
      'viewer': 1,
      'cashier': 2,
      'manager': 3,
      'admin': 4,
      'superadmin': 5
    };

    return roleHierarchy[userRole] >= roleHierarchy[role];
  }
};



