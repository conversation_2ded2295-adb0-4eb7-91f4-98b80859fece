// Script pour ajouter plus de factures de test avec des lignes de produits
const mysql = require('mysql2/promise');

async function addMoreInvoices() {
  let connection;
  
  try {
    // Connexion à la base de données
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom'
    });

    console.log('✅ Connexion à la base de données réussie');

    // Ajouter une deuxième facture
    console.log('📄 Création de la facture FACT-002...');
    const [result2] = await connection.execute(`
      INSERT INTO invoices (invoice_number, client_id, date, due_date, subtotal, tax_rate, tax_amount, total, notes, status)
      VALUES ('FACT-002', 2, '2024-05-26', '2024-06-26', 108999.95, 19, 20709.99, 129709.94, 'Commande urgente', 'unpaid')
    `);

    const invoice2Id = result2.insertId;
    console.log(`✅ Facture FACT-002 créée avec l'ID ${invoice2Id}`);

    // Ajouter des lignes pour la facture 2
    await connection.execute(`
      INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
      (${invoice2Id}, 2, 2, 49999.99, 0, 19, 99999.98),
      (${invoice2Id}, 4, 3, 2999.99, 0, 19, 8999.97)
    `);

    // Ajouter une troisième facture
    console.log('📄 Création de la facture FACT-003...');
    const [result3] = await connection.execute(`
      INSERT INTO invoices (invoice_number, client_id, date, due_date, subtotal, tax_rate, tax_amount, total, notes, status)
      VALUES ('FACT-003', 3, '2024-05-27', '2024-06-27', 264999.91, 19, 50349.98, 315349.89, 'Commande importante', 'paid')
    `);

    const invoice3Id = result3.insertId;
    console.log(`✅ Facture FACT-003 créée avec l'ID ${invoice3Id}`);

    // Ajouter des lignes pour la facture 3
    await connection.execute(`
      INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
      (${invoice3Id}, 1, 2, 89999.99, 0, 19, 179999.98),
      (${invoice3Id}, 2, 1, 49999.99, 0, 19, 49999.99),
      (${invoice3Id}, 3, 1, 19999.99, 0, 19, 19999.99),
      (${invoice3Id}, 4, 5, 2999.99, 0, 19, 14999.95)
    `);

    // Vérifier le résultat
    const [invoices] = await connection.execute('SELECT id, invoice_number, total FROM invoices ORDER BY id');
    console.log('\n📊 Factures créées:');
    for (const invoice of invoices) {
      const [items] = await connection.execute('SELECT COUNT(*) as count FROM invoice_items WHERE invoice_id = ?', [invoice.id]);
      console.log(`  - ${invoice.invoice_number}: ${invoice.total} DA (${items[0].count} lignes)`);
    }

    console.log('\n✅ Toutes les factures de test ont été créées avec succès !');

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addMoreInvoices();
