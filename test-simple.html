<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Simple - Routes</title>
</head>
<body>
  <h1>Test des Routes</h1>
  <div id="results"></div>
  
  <script>
    async function testRoutes() {
      const routes = [
        { method: 'GET', url: '/api/products', name: 'Produits' },
        { method: 'GET', url: '/api/suppliers', name: 'Fournisseurs' },
        { method: 'GET', url: '/api/purchase-orders', name: '<PERSON><PERSON> de commande (GET)' },
        { method: 'POST', url: '/api/purchase-orders', name: '<PERSON><PERSON> de commande (POST)' }
      ];
      
      const results = document.getElementById('results');
      
      for (const route of routes) {
        try {
          const options = {
            method: route.method,
            headers: {
              'Content-Type': 'application/json'
            }
          };
          
          if (route.method === 'POST') {
            options.body = JSON.stringify({
              date: '2024-01-20',
              supplierId: 1,
              items: [],
              subtotal: 0,
              taxRate: 19,
              taxAmount: 0,
              total: 0,
              notes: 'Test',
              status: 'En attente'
            });
          }
          
          const response = await fetch(`http://localhost:3000${route.url}`, options);
          
          const div = document.createElement('div');
          div.style.margin = '10px 0';
          div.style.padding = '10px';
          div.style.border = '1px solid #ccc';
          
          if (response.ok) {
            div.style.backgroundColor = '#d4edda';
            div.innerHTML = `✅ ${route.name}: ${response.status} ${response.statusText}`;
          } else {
            div.style.backgroundColor = '#f8d7da';
            div.innerHTML = `❌ ${route.name}: ${response.status} ${response.statusText}`;
          }
          
          results.appendChild(div);
          
        } catch (error) {
          const div = document.createElement('div');
          div.style.margin = '10px 0';
          div.style.padding = '10px';
          div.style.border = '1px solid #ccc';
          div.style.backgroundColor = '#f8d7da';
          div.innerHTML = `❌ ${route.name}: ${error.message}`;
          results.appendChild(div);
        }
      }
    }
    
    // Lancer le test au chargement
    window.addEventListener('load', testRoutes);
  </script>
</body>
</html>
