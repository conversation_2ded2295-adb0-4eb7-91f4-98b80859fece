// Script de test de connectivité réseau
const http = require('http');
const fs = require('fs');

async function testNetworkConnectivity() {
  console.log('🧪 Test de connectivité réseau GestiCom\n');
  
  // Lire la configuration réseau
  let config;
  try {
    const configData = fs.readFileSync('network-config.json', 'utf8');
    config = JSON.parse(configData);
  } catch (error) {
    console.log('❌ Fichier de configuration réseau non trouvé. Exécutez d\'abord: node setup-network.js');
    return;
  }
  
  console.log('📋 Configuration détectée:');
  console.log(`   - Serveur: ${config.hostname}`);
  console.log(`   - IP: ${config.serverIP}`);
  console.log(`   - Port: ${config.serverPort}`);
  console.log(`   - URL: ${config.accessURL}\n`);
  
  // Test 1: Accès local
  console.log('🔍 Test 1: Accès local (localhost)...');
  const localResult = await testURL(`http://localhost:${config.serverPort}`);
  console.log(localResult ? '✅ Accès local OK' : '❌ Accès local ÉCHEC');
  
  // Test 2: Accès réseau
  console.log('\n🔍 Test 2: Accès réseau (IP locale)...');
  const networkResult = await testURL(config.accessURL);
  console.log(networkResult ? '✅ Accès réseau OK' : '❌ Accès réseau ÉCHEC');
  
  // Test 3: API
  console.log('\n🔍 Test 3: Test de l\'API...');
  const apiResult = await testURL(`${config.accessURL}/api/products`);
  console.log(apiResult ? '✅ API fonctionnelle' : '❌ API non accessible');
  
  // Résumé
  console.log('\n📊 Résumé des tests:');
  console.log(`   Local: ${localResult ? '✅' : '❌'}`);
  console.log(`   Réseau: ${networkResult ? '✅' : '❌'}`);
  console.log(`   API: ${apiResult ? '✅' : '❌'}`);
  
  if (localResult && networkResult && apiResult) {
    console.log('\n🎉 Tous les tests sont réussis !');
    console.log('📱 Les autres appareils peuvent maintenant accéder à l\'application via:');
    console.log(`   ${config.accessURL}`);
  } else {
    console.log('\n⚠️  Certains tests ont échoué. Vérifiez:');
    if (!localResult) console.log('   - Le serveur est-il démarré ?');
    if (!networkResult) console.log('   - Le pare-feu bloque-t-il le port 3000 ?');
    if (!apiResult) console.log('   - La base de données est-elle accessible ?');
  }
  
  console.log('\n💡 Conseils:');
  console.log('   - Partagez cette URL avec les autres utilisateurs: ' + config.accessURL);
  console.log('   - Assurez-vous que tous les appareils sont sur le même réseau');
  console.log('   - Gardez le serveur en marche tant que l\'application est utilisée');
}

function testURL(url) {
  return new Promise((resolve) => {
    const request = http.get(url, (response) => {
      resolve(response.statusCode === 200);
    });
    
    request.on('error', () => {
      resolve(false);
    });
    
    request.setTimeout(5000, () => {
      request.destroy();
      resolve(false);
    });
  });
}

// Exécuter le test si le script est appelé directement
if (require.main === module) {
  testNetworkConnectivity().catch(console.error);
}

module.exports = { testNetworkConnectivity };
