<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Serveur - GestiCom</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #logs { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; height: 300px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Test du Serveur GestiCom</h1>
    
    <div class="test-result info">
        <strong>URL du serveur:</strong> <span id="serverUrl">http://localhost:3000</span>
    </div>
    
    <div>
        <button onclick="testConnection()">Tester la connexion</button>
        <button onclick="testAuth()">Tester l'authentification</button>
        <button onclick="testAPI()">Tester l'API</button>
        <button onclick="clearLogs()">Effacer les logs</button>
    </div>
    
    <div id="results"></div>
    
    <h3>Logs:</h3>
    <div id="logs"></div>

    <script>
        const serverUrl = 'http://localhost:3000';
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }
        
        function showResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }
        
        async function testConnection() {
            log('🧪 Test de connexion au serveur...');
            try {
                const response = await fetch(serverUrl + '/api/products');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Connexion réussie! ${data.length} produits récupérés`);
                    showResult('✅ Connexion au serveur réussie', 'success');
                } else {
                    log(`❌ Erreur HTTP: ${response.status}`);
                    showResult(`❌ Erreur de connexion: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Erreur de connexion: ${error.message}`);
                showResult(`❌ Impossible de se connecter au serveur: ${error.message}`, 'error');
            }
        }
        
        async function testAuth() {
            log('🔐 Test d\'authentification...');
            try {
                const response = await fetch(serverUrl + '/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username: 'admin', password: 'admin' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Authentification réussie! Token: ${data.token}`);
                    showResult('✅ Authentification réussie avec admin/admin', 'success');
                } else {
                    const errorData = await response.json();
                    log(`❌ Échec d'authentification: ${errorData.error}`);
                    showResult(`❌ Échec d'authentification: ${errorData.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Erreur d'authentification: ${error.message}`);
                showResult(`❌ Erreur d'authentification: ${error.message}`, 'error');
            }
        }
        
        async function testAPI() {
            log('📡 Test des APIs...');
            const endpoints = ['/api/products', '/api/clients', '/api/suppliers'];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(serverUrl + endpoint);
                    if (response.ok) {
                        const data = await response.json();
                        log(`✅ ${endpoint}: ${data.length} éléments récupérés`);
                    } else {
                        log(`❌ ${endpoint}: Erreur ${response.status}`);
                    }
                } catch (error) {
                    log(`❌ ${endpoint}: ${error.message}`);
                }
            }
            showResult('✅ Test des APIs terminé (voir les logs)', 'info');
        }
        
        // Test automatique au chargement
        window.addEventListener('load', () => {
            log('🚀 Page de test chargée');
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
