// Script pour vérifier la structure de la table users
const mysql = require('mysql2/promise');

async function checkTableStructure() {
  let connection;
  
  try {
    console.log('🔍 Vérification de la structure de la table users...');
    
    // Connexion à la base de données
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom'
    });

    console.log('✅ Connexion à la base de données réussie');

    // Vérifier la structure de la table
    const [columns] = await connection.execute("DESCRIBE users");
    
    console.log('\n📋 Structure de la table users:');
    console.log('┌─────────────────┬─────────────────┬──────┬─────┬─────────┬───────┐');
    console.log('│ Field           │ Type            │ Null │ Key │ Default │ Extra │');
    console.log('├─────────────────┼─────────────────┼──────┼─────┼─────────┼───────┤');
    
    columns.forEach(col => {
      const field = col.Field.padEnd(15);
      const type = col.Type.padEnd(15);
      const nullVal = col.Null.padEnd(4);
      const key = col.Key.padEnd(3);
      const defaultVal = (col.Default || '').toString().padEnd(7);
      const extra = col.Extra.padEnd(5);
      
      console.log(`│ ${field} │ ${type} │ ${nullVal} │ ${key} │ ${defaultVal} │ ${extra} │`);
    });
    
    console.log('└─────────────────┴─────────────────┴──────┴─────┴─────────┴───────┘');

    // Tester une insertion simple
    console.log('\n🧪 Test d\'insertion...');
    
    const testUser = {
      username: 'test_' + Date.now(),
      email: 'test_' + Date.now() + '@example.com',
      password_hash: 'test123',
      role: 'viewer',
      full_name: 'Test User',
      status: 'active'
    };
    
    try {
      const result = await connection.execute(`
        INSERT INTO users (username, email, password_hash, role, full_name, status)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        testUser.username,
        testUser.email,
        testUser.password_hash,
        testUser.role,
        testUser.full_name,
        testUser.status
      ]);
      
      console.log(`✅ Test d'insertion réussi, ID: ${result[0].insertId}`);
      
      // Supprimer l'utilisateur de test
      await connection.execute('DELETE FROM users WHERE id = ?', [result[0].insertId]);
      console.log('✅ Utilisateur de test supprimé');
      
    } catch (insertError) {
      console.error('❌ Erreur lors de l\'insertion:', insertError.message);
      console.error('Code d\'erreur:', insertError.code);
      console.error('SQL State:', insertError.sqlState);
    }

    // Vérifier les utilisateurs existants
    const [users] = await connection.execute('SELECT id, username, email, role, status FROM users');
    console.log('\n👥 Utilisateurs existants:');
    users.forEach(user => {
      console.log(`   - ID: ${user.id}, Username: ${user.username}, Role: ${user.role}, Status: ${user.status}`);
    });

  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('Code:', error.code);
    console.error('SQL State:', error.sqlState);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkTableStructure();
