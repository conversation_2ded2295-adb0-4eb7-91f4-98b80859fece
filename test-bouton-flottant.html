<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Bouton Flottant - GestiCom</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Custom CSS -->
  <link href="css/style.css" rel="stylesheet">
  <!-- Theme CSS -->
  <link href="css/themes.css" rel="stylesheet">
  
  <!-- Script pour appliquer le mode sombre immédiatement -->
  <script>
    // Appliquer le mode sombre par défaut immédiatement pour éviter le flash
    (function() {
      console.log('🔍 Initialisation du thème...');
      
      try {
        const savedTheme = localStorage.getItem('theme');
        console.log('📦 Thème sauvegardé:', savedTheme);
        
        // Définir le mode sombre par défaut si aucun thème n'est sauvegardé
        if (!savedTheme) {
          localStorage.setItem('theme', 'dark');
          document.documentElement.setAttribute('data-theme', 'dark');
          console.log('🌙 Mode sombre défini par défaut (première visite)');
        } else if (savedTheme === 'dark') {
          document.documentElement.setAttribute('data-theme', 'dark');
          console.log('🌙 Mode sombre appliqué depuis localStorage');
        } else {
          document.documentElement.removeAttribute('data-theme');
          console.log('☀️ Mode clair appliqué depuis localStorage');
        }
        
        // Vérification finale
        const finalTheme = document.documentElement.getAttribute('data-theme');
        console.log('✅ Attribut data-theme final:', finalTheme);
        
      } catch (error) {
        console.error('❌ Erreur lors de l\'initialisation du thème:', error);
        // En cas d'erreur, forcer quand même le mode sombre
        document.documentElement.setAttribute('data-theme', 'dark');
      }
    })();
  </script>
</head>
<body>
  <div class="container mt-5">
    <div class="row">
      <div class="col-12">
        <h1>Test du Bouton Flottant de Thème</h1>
        <p>Cette page teste le bouton flottant pour basculer entre les thèmes.</p>
        
        <div class="card mb-4">
          <div class="card-header">
            <h5>Instructions</h5>
          </div>
          <div class="card-body">
            <p>1. Regardez en haut à droite de la page - vous devriez voir un bouton rond flottant</p>
            <p>2. Cliquez sur ce bouton pour basculer entre le mode sombre et le mode clair</p>
            <p>3. L'icône du bouton devrait changer (lune/soleil) selon le thème actuel</p>
            <p>4. Les couleurs de la page devraient changer instantanément</p>
          </div>
        </div>
        
        <div class="card mb-4">
          <div class="card-header">
            <h5>État actuel du thème</h5>
          </div>
          <div class="card-body">
            <p>Thème actuel : <span id="currentTheme" class="badge bg-primary">Chargement...</span></p>
            <p>Attribut data-theme : <span id="dataTheme" class="badge bg-secondary">Chargement...</span></p>
            <p>LocalStorage : <span id="localStorage" class="badge bg-info">Chargement...</span></p>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h5>Éléments de test</h5>
          </div>
          <div class="card-body">
            <button class="btn btn-primary me-2">Bouton Primary</button>
            <button class="btn btn-secondary me-2">Bouton Secondary</button>
            <button class="btn btn-success me-2">Bouton Success</button>
            <button class="btn btn-danger me-2">Bouton Danger</button>
            
            <div class="mt-3">
              <div class="alert alert-info">
                <strong>Info :</strong> Cette alerte teste les couleurs en mode sombre/clair.
              </div>
            </div>
            
            <table class="table table-striped mt-3">
              <thead>
                <tr>
                  <th>Nom</th>
                  <th>Valeur</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Test 1</td>
                  <td>Valeur 1</td>
                  <td><span class="badge bg-success">Actif</span></td>
                </tr>
                <tr>
                  <td>Test 2</td>
                  <td>Valeur 2</td>
                  <td><span class="badge bg-warning">En attente</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Theme JS -->
  <script src="js/theme.js"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Fonction pour mettre à jour les informations de thème
      function updateThemeInfo() {
        const currentThemeSpan = document.getElementById('currentTheme');
        const dataThemeSpan = document.getElementById('dataTheme');
        const localStorageSpan = document.getElementById('localStorage');
        
        if (window.themeManager) {
          const currentTheme = window.themeManager.getCurrentTheme();
          currentThemeSpan.textContent = currentTheme;
          currentThemeSpan.className = `badge ${currentTheme === 'dark' ? 'bg-dark' : 'bg-light text-dark'}`;
        } else {
          currentThemeSpan.textContent = 'ThemeManager non disponible';
          currentThemeSpan.className = 'badge bg-danger';
        }
        
        const dataTheme = document.documentElement.getAttribute('data-theme');
        dataThemeSpan.textContent = dataTheme || 'aucun (mode clair)';
        dataThemeSpan.className = `badge ${dataTheme === 'dark' ? 'bg-dark' : 'bg-secondary'}`;
        
        const savedTheme = localStorage.getItem('theme');
        localStorageSpan.textContent = savedTheme || 'aucun';
        localStorageSpan.className = `badge ${savedTheme === 'dark' ? 'bg-dark' : 'bg-info'}`;
      }
      
      // Mettre à jour les infos au chargement
      setTimeout(updateThemeInfo, 500);
      
      // Écouter les changements de thème
      document.addEventListener('themeChanged', function() {
        setTimeout(updateThemeInfo, 100);
      });
      
      // Mettre à jour toutes les 2 secondes pour voir les changements
      setInterval(updateThemeInfo, 2000);
    });
  </script>
</body>
</html>
