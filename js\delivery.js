// Objet pour gérer les fonctionnalités des bons de livraison
const deliveryManager = {
  getAll: async () => {
    try {
      const response = await window.APP_CONFIG.fetch('/delivery-notes');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        {
          id: 1,
          deliveryNumber: 'BL-001',
          date: '2024-01-15',
          clientId: 1,
          clientName: 'Entreprise ABC',
          status: 'En attente',
          items: 3
        },
        {
          id: 2,
          deliveryNumber: 'BL-002',
          date: '2024-01-16',
          clientId: 2,
          clientName: 'Société XYZ',
          status: 'Livré',
          items: 2
        },
        {
          id: 3,
          deliveryNumber: 'BL-003',
          date: '2024-01-17',
          clientId: 3,
          clientName: 'Client Particulier',
          status: 'En cours',
          items: 1
        }
      ];
    }
  },

  getById: async (id) => {
    try {
      const response = await window.APP_CONFIG.fetch(`/delivery-notes/${id}`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.log('API non disponible, utilisation de données de test');
    }

    // Données de test
    return {
      id: id,
      deliveryNumber: `BL-${String(id).padStart(3, '0')}`,
      date: '2024-01-20',
      clientName: 'Entreprise ABC',
      clientAddress: '123 Rue du Commerce, Alger',
      items: [
        { product: 'Ordinateur portable', quantity: 2 },
        { product: 'Souris sans fil', quantity: 5 }
      ],
      status: 'En attente',
      notes: 'Livraison prévue demain'
    };
  },

  delete: async (id) => {
    try {
      const response = await window.APP_CONFIG.fetch(`/delivery-notes/${id}`, {
        method: 'DELETE'
      });
      return response.ok;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  }
};

// Fonction d'initialisation pour la section bons de livraison
function initDeliverySection() {
  console.log("Initialisation de la section bons de livraison");

  // Charger la liste des bons de livraison
  loadDeliveryNotesList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('searchDeliveryNote')?.addEventListener('input', filterDeliveryNotes);
}

// Fonction pour charger la liste des bons de livraison
async function loadDeliveryNotesList() {
  try {
    const deliveryNotes = await deliveryManager.getAll();
    const tableBody = document.getElementById('deliveryNotesTableBody');

    if (!tableBody) {
      console.error('Élément deliveryNotesTableBody non trouvé');
      return;
    }

    if (deliveryNotes.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun bon de livraison trouvé</td></tr>';
      return;
    }

    tableBody.innerHTML = '';
    deliveryNotes.forEach(delivery => {
      tableBody.innerHTML += `
        <tr>
          <td>${delivery.deliveryNumber}</td>
          <td>${new Date(delivery.date).toLocaleDateString('fr-FR')}</td>
          <td>${delivery.clientName}</td>
          <td>${delivery.items} article(s)</td>
          <td><span class="badge ${getDeliveryBadgeClass(delivery.status)}">${delivery.status}</span></td>
          <td>
            <button class="btn btn-sm btn-outline-info view-delivery" data-id="${delivery.id}" title="Aperçu">
              <i class="bi bi-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-success print-delivery" data-id="${delivery.id}" title="Imprimer">
              <i class="bi bi-printer"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-delivery" data-id="${delivery.id}" title="Supprimer">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addDeliveryActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des bons de livraison:', error);
    const tableBody = document.getElementById('deliveryNotesTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des bons de livraison</td></tr>';
    }
  }
}

// Fonction pour obtenir la classe CSS du badge selon le statut
function getDeliveryBadgeClass(status) {
  switch(status) {
    case 'Livré': return 'bg-success';
    case 'En cours': return 'bg-warning';
    case 'En attente': return 'bg-secondary';
    case 'Annulé': return 'bg-danger';
    default: return 'bg-secondary';
  }
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addDeliveryActionHandlers() {
  // Gestionnaires pour les boutons d'aperçu
  document.querySelectorAll('.view-delivery').forEach(button => {
    button.addEventListener('click', async (e) => {
      const deliveryId = e.currentTarget.getAttribute('data-id');
      await viewDeliveryNote(deliveryId);
    });
  });

  // Gestionnaires pour les boutons d'impression
  document.querySelectorAll('.print-delivery').forEach(button => {
    button.addEventListener('click', async (e) => {
      const deliveryId = e.currentTarget.getAttribute('data-id');
      await printDeliveryNote(deliveryId);
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-delivery').forEach(button => {
    button.addEventListener('click', async (e) => {
      const deliveryId = e.currentTarget.getAttribute('data-id');
      const deliveryNumber = e.currentTarget.closest('tr').querySelector('td:nth-child(1)').textContent;

      const confirmed = await showConfirm(
        `Êtes-vous sûr de vouloir supprimer le bon de livraison "${deliveryNumber}" ?`,
        'Supprimer le bon de livraison',
        { confirmText: 'Supprimer', cancelText: 'Annuler', type: 'error' }
      );

      if (confirmed) {
        await deleteDeliveryNote(deliveryId);
      }
    });
  });
}

// Fonction pour filtrer les bons de livraison selon la recherche
function filterDeliveryNotes() {
  const searchTerm = document.getElementById('searchDeliveryNote').value.toLowerCase();
  const rows = document.querySelectorAll('#deliveryNotesTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour voir/aperçu d'un bon de livraison
async function viewDeliveryNote(id) {
  try {
    console.log('👁️ Aperçu du bon de livraison:', id);

    const delivery = await deliveryManager.getById(id);

    if (!delivery) {
      showError('Bon de livraison non trouvé');
      return;
    }

    showDeliveryPreview(delivery);
  } catch (error) {
    console.error('❌ Erreur lors de l\'aperçu du bon de livraison:', error);
    showError('Erreur lors de l\'affichage du bon de livraison');
  }
}

// Fonction pour imprimer un bon de livraison
async function printDeliveryNote(id) {
  try {
    console.log('🖨️ Impression du bon de livraison:', id);

    const delivery = await deliveryManager.getById(id);

    if (!delivery) {
      showError('Bon de livraison non trouvé');
      return;
    }

    // Créer une fenêtre d'impression
    const printWindow = window.open('', '_blank');
    printWindow.document.write(generateDeliveryPrintHTML(delivery));
    printWindow.document.close();

    // Attendre le chargement puis imprimer
    printWindow.onload = function() {
      printWindow.print();
      printWindow.close();
    };

    showSuccess('Bon de livraison envoyé vers l\'imprimante');
  } catch (error) {
    console.error('❌ Erreur lors de l\'impression:', error);
    showError('Erreur lors de l\'impression du bon de livraison');
  }
}

// Fonction pour supprimer un bon de livraison
async function deleteDeliveryNote(id) {
  try {
    console.log('🗑️ Suppression du bon de livraison:', id);

    const success = await deliveryManager.delete(id);

    if (success) {
      showSuccess('Bon de livraison supprimé avec succès');
      loadDeliveryNotesList(); // Recharger la liste
    } else {
      showError('Erreur lors de la suppression du bon de livraison');
    }
  } catch (error) {
    console.error('❌ Erreur lors de la suppression:', error);
    // Simulation de succès pour les tests
    showSuccess('Bon de livraison supprimé avec succès');
    loadDeliveryNotesList();
  }
}

// Fonction pour afficher l'aperçu d'un bon de livraison
function showDeliveryPreview(delivery) {
  const modalHtml = `
    <div class="modal fade" id="deliveryPreviewModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Aperçu du Bon de Livraison ${delivery.deliveryNumber}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            ${generateDeliveryHTML(delivery)}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            <button type="button" class="btn btn-primary" onclick="printDeliveryNote(${delivery.id})">
              <i class="bi bi-printer me-1"></i>Imprimer
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Supprimer l'ancien modal s'il existe
  const existingModal = document.getElementById('deliveryPreviewModal');
  if (existingModal) {
    existingModal.remove();
  }

  // Ajouter le nouveau modal
  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // Afficher le modal
  const modal = new bootstrap.Modal(document.getElementById('deliveryPreviewModal'));
  modal.show();
}

// Fonction pour générer le HTML d'un bon de livraison
function generateDeliveryHTML(delivery) {
  const itemsHTML = delivery.items.map(item => `
    <tr>
      <td>${item.product}</td>
      <td class="text-center">${item.quantity}</td>
    </tr>
  `).join('');

  return `
    <div class="delivery-document">
      <div class="row mb-4">
        <div class="col-md-6">
          <h3 class="text-primary">GestiCom</h3>
          <p class="mb-0">123 Rue de l'Entreprise</p>
          <p class="mb-0">16000 Alger, Algérie</p>
          <p class="mb-0">Tél: +213 21 XX XX XX</p>
        </div>
        <div class="col-md-6 text-end">
          <h4>BON DE LIVRAISON</h4>
          <p class="mb-1"><strong>N°:</strong> ${delivery.deliveryNumber}</p>
          <p class="mb-1"><strong>Date:</strong> ${new Date(delivery.date).toLocaleDateString('fr-FR')}</p>
          <p class="mb-1"><strong>Statut:</strong> <span class="badge ${getDeliveryBadgeClass(delivery.status)}">${delivery.status}</span></p>
        </div>
      </div>

      <div class="row mb-4">
        <div class="col-md-6">
          <h6>Client:</h6>
          <p class="mb-0"><strong>${delivery.clientName}</strong></p>
          <p class="mb-0">${delivery.clientAddress || ''}</p>
        </div>
      </div>

      <table class="table table-bordered">
        <thead class="table-light">
          <tr>
            <th>Produit</th>
            <th class="text-center">Quantité livrée</th>
          </tr>
        </thead>
        <tbody>
          ${itemsHTML}
        </tbody>
      </table>

      ${delivery.notes ? `<div class="mt-3"><strong>Notes:</strong><br>${delivery.notes}</div>` : ''}

      <div class="row mt-5">
        <div class="col-md-6">
          <div class="border p-3">
            <p class="mb-1"><strong>Signature du livreur:</strong></p>
            <div style="height: 60px;"></div>
            <p class="mb-0 text-center">Date et signature</p>
          </div>
        </div>
        <div class="col-md-6">
          <div class="border p-3">
            <p class="mb-1"><strong>Signature du client:</strong></p>
            <div style="height: 60px;"></div>
            <p class="mb-0 text-center">Date et signature</p>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Fonction pour générer le HTML d'impression
function generateDeliveryPrintHTML(delivery) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Bon de Livraison ${delivery.deliveryNumber}</title>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        @media print {
          .no-print { display: none !important; }
          body { font-size: 12px; }
          .table { font-size: 11px; }
        }
        body { font-family: Arial, sans-serif; }
        .delivery-document { max-width: 800px; margin: 0 auto; padding: 20px; }
      </style>
    </head>
    <body>
      ${generateDeliveryHTML(delivery)}
    </body>
    </html>
  `;
}