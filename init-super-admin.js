// Script d'initialisation pour le Super Admin
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function initSuperAdmin() {
  let connection;
  
  try {
    console.log('🔧 Initialisation du Super Admin...\n');
    
    // Connexion à la base de données
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom'
    });

    console.log('✅ Connexion à la base de données réussie');

    // Créer la table des utilisateurs si elle n'existe pas
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role ENUM('superadmin', 'admin', 'manager', 'cashier', 'viewer') DEFAULT 'viewer',
        full_name VARCHAR(100),
        phone VARCHAR(20),
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ Table users créée/vérifiée');

    // Créer la table de configuration système
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT,
        description VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ Table system_config créée/vérifiée');

    // Créer la table des sessions utilisateurs
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_token VARCHAR(255) NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    console.log('✅ Table user_sessions créée/vérifiée');

    // Vérifier si le super admin existe déjà
    const [existingAdmin] = await connection.execute(
      'SELECT id FROM users WHERE role = "superadmin" LIMIT 1'
    );

    if (existingAdmin.length === 0) {
      // Créer le compte super admin par défaut
      const defaultPassword = 'admin123';
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);

      await connection.execute(`
        INSERT INTO users (username, email, password_hash, role, full_name, status)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        'admin',
        '<EMAIL>',
        hashedPassword,
        'superadmin',
        'Super Administrateur',
        'active'
      ]);

      console.log('✅ Compte Super Admin créé');
      console.log('   - Nom d\'utilisateur: admin');
      console.log('   - Mot de passe: admin123');
      console.log('   - ⚠️  CHANGEZ CE MOT DE PASSE IMMÉDIATEMENT !');
    } else {
      console.log('ℹ️  Compte Super Admin déjà existant');
    }

    // Créer des utilisateurs de test
    const testUsers = [
      {
        username: 'manager1',
        email: '<EMAIL>',
        password: 'manager123',
        role: 'manager',
        fullName: 'Gestionnaire Commercial'
      },
      {
        username: 'cashier1',
        email: '<EMAIL>',
        password: 'caisse123',
        role: 'cashier',
        fullName: 'Caissier Principal'
      },
      {
        username: 'viewer1',
        email: '<EMAIL>',
        password: 'viewer123',
        role: 'viewer',
        fullName: 'Utilisateur Consultation'
      }
    ];

    for (const user of testUsers) {
      const [existing] = await connection.execute(
        'SELECT id FROM users WHERE username = ?',
        [user.username]
      );

      if (existing.length === 0) {
        const hashedPassword = await bcrypt.hash(user.password, 10);
        
        await connection.execute(`
          INSERT INTO users (username, email, password_hash, role, full_name, status)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          user.username,
          user.email,
          hashedPassword,
          user.role,
          user.fullName,
          'active'
        ]);

        console.log(`✅ Utilisateur ${user.username} créé (${user.role})`);
      }
    }

    // Insérer la configuration système par défaut
    const defaultConfigs = [
      {
        key: 'server_port',
        value: '3000',
        description: 'Port du serveur web'
      },
      {
        key: 'max_connections',
        value: '50',
        description: 'Nombre maximum de connexions simultanées'
      },
      {
        key: 'session_timeout',
        value: '3600',
        description: 'Timeout des sessions en secondes'
      },
      {
        key: 'enable_ssl',
        value: 'false',
        description: 'Activer HTTPS'
      },
      {
        key: 'log_level',
        value: 'info',
        description: 'Niveau de logging'
      },
      {
        key: 'backup_retention_days',
        value: '30',
        description: 'Nombre de jours de rétention des sauvegardes'
      }
    ];

    for (const config of defaultConfigs) {
      await connection.execute(`
        INSERT INTO system_config (config_key, config_value, description)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE
        config_value = VALUES(config_value),
        description = VALUES(description)
      `, [config.key, config.value, config.description]);
    }

    console.log('✅ Configuration système initialisée');

    // Afficher le résumé
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM system_config');

    console.log('\n📊 Résumé de l\'initialisation:');
    console.log(`   - ${userCount[0].count} utilisateurs dans la base`);
    console.log(`   - ${configCount[0].count} paramètres de configuration`);
    console.log('   - Tables système créées');

    console.log('\n🚀 Pour accéder au Super Admin:');
    console.log('   1. Démarrez le serveur: node server.js');
    console.log('   2. Ouvrez l\'application dans le navigateur');
    console.log('   3. Connectez-vous avec: admin / admin123');
    console.log('   4. Cliquez sur "Super Admin" dans le menu');

    console.log('\n🔐 Sécurité:');
    console.log('   - Changez IMMÉDIATEMENT le mot de passe par défaut');
    console.log('   - Configurez des mots de passe forts pour tous les utilisateurs');
    console.log('   - Limitez l\'accès Super Admin aux personnes autorisées');

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Exécuter l'initialisation si le script est appelé directement
if (require.main === module) {
  initSuperAdmin().then(() => {
    console.log('\n✅ Initialisation Super Admin terminée !');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = { initSuperAdmin };
