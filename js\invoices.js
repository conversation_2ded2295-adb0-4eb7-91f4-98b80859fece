// Objet pour gérer les fonctionnalités des factures
const invoiceManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/invoices');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        {
          id: 1,
          invoiceNumber: 'FACT-001',
          date: '2023-05-15',
          clientId: 1,
          clientName: 'Entreprise ABC',
          total: 1200.50,
          status: 'Payée',
          dueDate: '2023-06-15'
        },
        {
          id: 2,
          invoiceNumber: 'FACT-002',
          date: '2023-05-20',
          clientId: 2,
          clientName: 'Société XYZ',
          total: 850.75,
          status: 'En attente',
          dueDate: '2023-06-20'
        },
        {
          id: 3,
          invoiceNumber: 'FACT-003',
          date: '2023-05-25',
          clientId: 3,
          clientName: 'Client Particulier',
          total: 350.00,
          status: 'En retard',
          dueDate: '2023-05-25'
        }
      ];
    }
  },

  getById: async (id) => {
    try {
      const response = await fetch(`/api/invoices/${id}`);
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de récupération");
      // Retourner des données de test complètes
      return {
        id: parseInt(id),
        invoiceNumber: `FACT-${String(id).padStart(3, '0')}`,
        date: '2024-01-20',
        clientId: 1,
        clientName: 'Entreprise ABC',
        clientEmail: '<EMAIL>',
        clientPhone: '+213 21 XX XX XX',
        clientAddress: '123 Rue du Commerce, Alger',
        subtotal: 1000.00,
        taxRate: 19,
        taxAmount: 190.00,
        total: 1190.00,
        amountPaid: 0,
        status: 'unpaid',
        notes: 'Facture de test',
        items: [
          {
            id: 1,
            productId: 1,
            productName: 'Ordinateur portable',
            productDescription: 'Ordinateur portable haute performance',
            quantity: 1,
            unitPrice: 850.00,
            discountPercent: 0,
            total: 850.00
          },
          {
            id: 2,
            productId: 2,
            productName: 'Souris sans fil',
            productDescription: 'Souris optique sans fil',
            quantity: 1,
            unitPrice: 150.00,
            discountPercent: 0,
            total: 150.00
          }
        ]
      };
    }
  },

  add: async (invoice) => {
    try {
      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(invoice)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      return {
        ...invoice,
        id: Math.floor(Math.random() * 1000) + 10,
        invoiceNumber: `FACT-${String(Math.floor(Math.random() * 1000) + 100).padStart(3, '0')}`
      };
    }
  },

  update: async (id, data) => {
    try {
      const response = await fetch(`/api/invoices/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      return { ...data, id: parseInt(id) };
    }
  },

  delete: async (id) => {
    try {
      await fetch(`/api/invoices/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return true;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  }
};

// Fonction d'initialisation pour la section factures
function initInvoicesSection() {
  console.log("Initialisation de la section factures");

  // Charger la liste des factures
  loadInvoicesList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('saveInvoiceBtn')?.addEventListener('click', saveInvoice);
  document.getElementById('searchInvoice')?.addEventListener('input', filterInvoices);
  document.getElementById('addInvoiceItem')?.addEventListener('click', addInvoiceItem);

  // Gestionnaire pour le bouton "Nouvelle facture"
  document.querySelector('[data-bs-target="#addInvoiceModal"]')?.addEventListener('click', () => {
    resetInvoiceForm();
  });

  // Charger les clients pour le sélecteur
  loadClientsForInvoice();

  // Charger les produits pour le sélecteur
  loadProductsForInvoice();

  // Initialiser les calculs
  setupInvoiceCalculations();

  // Gestionnaires pour les boutons du modal d'aperçu
  document.getElementById('editFromPreview')?.addEventListener('click', (e) => {
    const invoiceId = e.currentTarget.getAttribute('data-invoice-id');
    if (invoiceId) {
      // Fermer le modal d'aperçu
      const viewModal = bootstrap.Modal.getInstance(document.getElementById('viewInvoiceModal'));
      viewModal.hide();

      // Ouvrir le modal d'édition
      setTimeout(() => {
        editInvoice(invoiceId);
      }, 300);
    }
  });

  document.getElementById('printFromPreview')?.addEventListener('click', (e) => {
    const invoiceId = e.currentTarget.getAttribute('data-invoice-id');
    if (invoiceId) {
      printInvoice(invoiceId);
    }
  });
}

// Fonction pour charger la liste des factures
async function loadInvoicesList() {
  try {
    const invoices = await invoiceManager.getAll();
    const tableBody = document.getElementById('invoicesTableBody');

    if (!tableBody) {
      console.error('Élément invoicesTableBody non trouvé');
      return;
    }

    if (invoices.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucune facture trouvée</td></tr>';
      return;
    }

    tableBody.innerHTML = '';
    invoices.forEach(invoice => {
      tableBody.innerHTML += `
        <tr>
          <td>${invoice.invoiceNumber}</td>
          <td>${new Date(invoice.date).toLocaleDateString()}</td>
          <td>${invoice.clientName}</td>
          <td>${invoice.total.toFixed(2)} DA</td>
          <td><span class="badge ${getBadgeClass(invoice.status)}">${invoice.status}</span></td>
          <td>
            <button class="btn btn-sm btn-outline-info view-invoice" data-id="${invoice.id}" title="Aperçu">
              <i class="bi bi-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary edit-invoice" data-id="${invoice.id}" title="Modifier">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-success print-invoice" data-id="${invoice.id}" title="Imprimer">
              <i class="bi bi-printer"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-invoice" data-id="${invoice.id}" title="Supprimer">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addInvoiceActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des factures:', error);
    const tableBody = document.getElementById('invoicesTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des factures</td></tr>';
    }
  }
}

// Fonction pour obtenir la classe CSS du badge selon le statut
function getBadgeClass(status) {
  switch(status) {
    case 'Payée': return 'bg-success';
    case 'En attente': return 'bg-warning';
    case 'En retard': return 'bg-danger';
    case 'Annulée': return 'bg-secondary';
    default: return 'bg-secondary';
  }
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addInvoiceActionHandlers() {
  // Gestionnaires pour les boutons d'aperçu
  document.querySelectorAll('.view-invoice').forEach(button => {
    button.addEventListener('click', async (e) => {
      const invoiceId = e.currentTarget.getAttribute('data-id');
      await showInvoicePreview(invoiceId);
    });
  });

  // Gestionnaires pour les boutons d'édition
  document.querySelectorAll('.edit-invoice').forEach(button => {
    button.addEventListener('click', async (e) => {
      const invoiceId = e.currentTarget.getAttribute('data-id');
      await editInvoice(invoiceId);
    });
  });

  // Gestionnaires pour les boutons d'impression
  document.querySelectorAll('.print-invoice').forEach(button => {
    button.addEventListener('click', (e) => {
      const invoiceId = e.currentTarget.getAttribute('data-id');
      printInvoice(invoiceId);
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-invoice').forEach(button => {
    button.addEventListener('click', async (e) => {
      const invoiceId = e.currentTarget.getAttribute('data-id');
      const invoiceNumber = e.currentTarget.closest('tr').querySelector('td:nth-child(1)').textContent;

      const confirmed = await showConfirm(
        `Êtes-vous sûr de vouloir supprimer la facture "${invoiceNumber}" ?`,
        'Supprimer la facture',
        { confirmText: 'Supprimer', cancelText: 'Annuler', type: 'error' }
      );

      if (confirmed) {
        try {
          await invoiceManager.delete(invoiceId);
          showSuccess('Facture supprimée avec succès');
          loadInvoicesList();
        } catch (error) {
          console.error('Erreur lors de la suppression de la facture:', error);
          showError('Erreur lors de la suppression de la facture: ' + error.message);
        }
      }
    });
  });
}

// Fonction pour filtrer les factures selon la recherche
function filterInvoices() {
  const searchTerm = document.getElementById('searchInvoice').value.toLowerCase();
  const rows = document.querySelectorAll('#invoicesTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour sauvegarder une facture (nouvelle ou mise à jour)
async function saveInvoice() {
  const invoiceId = document.getElementById('invoiceId')?.value;
  console.log('💾 Sauvegarde de la facture, ID:', invoiceId);

  // Récupérer les valeurs du formulaire
  const date = document.getElementById('invoiceDate').value;
  const clientId = document.getElementById('invoiceClient').value;
  const status = document.getElementById('invoiceStatus').value;
  const notes = document.getElementById('invoiceNote').value.trim();

  console.log('📋 Données du formulaire:', { date, clientId, status, notes });

  // Validation basique
  if (!date) {
    showError('La date de la facture est obligatoire');
    return;
  }

  if (!clientId) {
    showError('Le client est obligatoire');
    return;
  }

  // Récupérer les lignes de produits
  const productRows = document.querySelectorAll('#invoiceItemsTableBody tr');
  if (productRows.length === 0) {
    showError('Veuillez ajouter au moins un produit à la facture');
    return;
  }

  // Collecter les données des lignes de produits
  const items = [];
  let subtotal = 0;

  productRows.forEach(row => {
    const productSelect = row.querySelector('.product-select');
    const quantityInput = row.querySelector('.quantity');
    const priceInput = row.querySelector('.unit-price');
    const totalInput = row.querySelector('.line-total');

    if (productSelect && productSelect.value && quantityInput && priceInput) {
      const productId = parseInt(productSelect.value);
      const quantity = parseInt(quantityInput.value) || 0;
      const unitPrice = parseFloat(priceInput.value) || 0;
      const lineTotal = parseFloat(totalInput.value) || 0;

      if (productId && quantity > 0 && unitPrice > 0) {
        items.push({
          productId: productId,
          quantity: quantity,
          unitPrice: unitPrice,
          discountPercent: 0, // À implémenter si nécessaire
          taxPercent: 19, // TVA fixe à 19%
          total: lineTotal
        });

        subtotal += lineTotal;
      }
    }
  });

  if (items.length === 0) {
    showError('Veuillez ajouter au moins un produit valide à la facture');
    return;
  }

  const taxRate = 19.00; // TVA fixe à 19%
  const taxAmount = (subtotal * taxRate) / 100;
  const total = subtotal + taxAmount;

  const invoice = {
    clientId: parseInt(clientId),
    date: date,
    dueDate: null, // À implémenter si nécessaire
    subtotal: subtotal,
    taxRate: taxRate,
    taxAmount: taxAmount,
    total: total,
    status: status || 'unpaid',
    notes: notes || null,
    items: items
  };

  console.log('Données de la facture à envoyer:', invoice);

  try {
    let result;
    if (invoiceId) {
      // Mise à jour d'une facture existante
      console.log(`Mise à jour de la facture ${invoiceId}`);
      result = await invoiceManager.update(invoiceId, invoice);
    } else {
      // Ajout d'une nouvelle facture
      console.log('Ajout d\'une nouvelle facture');
      result = await invoiceManager.add(invoice);
    }

    console.log('Résultat:', result);

    // Fermer le modal et rafraîchir la liste
    const modal = bootstrap.Modal.getInstance(document.getElementById('addInvoiceModal'));
    modal.hide();

    // Réinitialiser le formulaire
    document.getElementById('addInvoiceForm').reset();
    document.getElementById('invoiceId').value = '';
    document.getElementById('addInvoiceModalLabel').textContent = 'Nouvelle facture';

    // Vider le tableau des produits
    document.getElementById('invoiceItemsTableBody').innerHTML = '';

    // Rafraîchir la liste
    loadInvoicesList();

    // Afficher un message de succès
    showSuccess(invoiceId ? 'Facture modifiée avec succès' : 'Facture créée avec succès');

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement de la facture:', error);
    showError('Erreur lors de l\'enregistrement de la facture: ' + error.message);
  }
}

// Fonction pour ajouter une ligne de produit à la facture
async function addInvoiceItem() {
  const tableBody = document.getElementById('invoiceItemsTableBody');

  if (!tableBody) {
    console.error("Table des items de facture non trouvée");
    return;
  }

  // Créer une nouvelle ligne
  const row = document.createElement('tr');
  row.innerHTML = `
    <td>
      <select class="form-select product-select" required>
        <option value="">Sélectionner un produit</option>
      </select>
    </td>
    <td>
      <input type="number" class="form-control quantity" min="1" value="1" required>
    </td>
    <td>
      <input type="number" class="form-control unit-price" min="0" step="0.01" required>
    </td>
    <td>
      <input type="number" class="form-control line-total" readonly>
    </td>
    <td>
      <button type="button" class="btn btn-danger btn-sm remove-item">
        <i class="bi bi-trash"></i>
      </button>
    </td>
  `;

  tableBody.appendChild(row);

  // Charger les produits dans le nouveau sélecteur
  const productSelect = row.querySelector('.product-select');
  try {
    const products = await productManager.getAll();

    products.forEach(product => {
      const option = document.createElement('option');
      option.value = product.id;
      option.textContent = product.name;
      option.dataset.price = product.price;
      productSelect.appendChild(option);
    });

    // Ajouter les gestionnaires d'événements
    productSelect.addEventListener('change', updateInvoiceProductPrice);

    const quantityInput = row.querySelector('.quantity');
    const priceInput = row.querySelector('.unit-price');

    quantityInput.addEventListener('input', () => calculateInvoiceLineTotal(row));
    priceInput.addEventListener('input', () => calculateInvoiceLineTotal(row));

    // Gestionnaire pour supprimer la ligne
    const removeBtn = row.querySelector('.remove-item');
    removeBtn.addEventListener('click', () => {
      row.remove();
      calculateInvoiceTotal();
    });

  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
  }
}

// Fonction pour charger les clients dans le sélecteur
async function loadClientsForInvoice() {
  try {
    console.log("Chargement des clients pour les factures...");
    const clients = await clientManager.getAll();
    const clientSelect = document.getElementById('invoiceClient');

    if (!clientSelect) {
      console.error("Élément invoiceClient non trouvé");
      return;
    }

    // Conserver l'option par défaut
    clientSelect.innerHTML = '<option value="">Sélectionner un client</option>';

    clients.forEach(client => {
      const option = document.createElement('option');
      option.value = client.id;
      option.textContent = client.name;
      clientSelect.appendChild(option);
    });

    console.log(`${clients.length} clients chargés dans le sélecteur`);
  } catch (error) {
    console.error('Erreur lors du chargement des clients:', error);
  }
}

// Fonction pour charger les produits dans le sélecteur
async function loadProductsForInvoice() {
  try {
    console.log("Chargement des produits pour les factures...");
    const products = await productManager.getAll();
    const productSelects = document.querySelectorAll('.product-select');

    if (productSelects.length === 0) {
      console.warn("Aucun sélecteur de produit trouvé");
      return;
    }

    productSelects.forEach(select => {
      // Conserver l'option par défaut
      select.innerHTML = '<option value="">Sélectionner un produit</option>';

      products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = product.name;
        option.dataset.price = product.price;
        select.appendChild(option);
      });

      // Ajouter le gestionnaire d'événement pour le changement de produit
      select.addEventListener('change', updateInvoiceProductPrice);
    });

    console.log(`${products.length} produits chargés dans les sélecteurs`);
  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
  }
}

// Fonction pour mettre à jour le prix quand un produit est sélectionné
function updateInvoiceProductPrice(event) {
  const select = event.target;
  const selectedOption = select.options[select.selectedIndex];

  if (selectedOption && selectedOption.dataset.price) {
    const row = select.closest('tr');
    const priceInput = row.querySelector('.unit-price');

    if (priceInput) {
      priceInput.value = parseFloat(selectedOption.dataset.price).toFixed(2);
      // Déclencher le calcul du total de la ligne
      calculateInvoiceLineTotal(row);
    }
  }
}

// Fonction pour calculer le total d'une ligne de facture
function calculateInvoiceLineTotal(row) {
  const quantity = parseFloat(row.querySelector('.quantity')?.value || 0);
  const unitPrice = parseFloat(row.querySelector('.unit-price')?.value || 0);
  const total = quantity * unitPrice;

  const totalInput = row.querySelector('.line-total');
  if (totalInput) {
    totalInput.value = total.toFixed(2);
  }

  // Recalculer le total général de la facture
  calculateInvoiceTotal();
}

// Fonction pour calculer le total général de la facture
function calculateInvoiceTotal() {
  const lineTotals = document.querySelectorAll('.line-total');
  let subtotal = 0;

  lineTotals.forEach(input => {
    subtotal += parseFloat(input.value || 0);
  });

  const taxRate = 19.00; // TVA fixe à 19%
  const taxAmount = (subtotal * taxRate) / 100;
  const total = subtotal + taxAmount;

  // Mettre à jour les champs de total
  const subtotalField = document.getElementById('invoiceSubtotal');
  const taxField = document.getElementById('invoiceTax');
  const totalField = document.getElementById('invoiceTotal');

  if (subtotalField) subtotalField.value = subtotal.toFixed(2) + ' DA';
  if (taxField) taxField.value = taxAmount.toFixed(2) + ' DA';
  if (totalField) totalField.value = total.toFixed(2) + ' DA';
}

function setupInvoiceCalculations() {
  // Ajouter les gestionnaires d'événements pour les calculs automatiques
  document.addEventListener('input', (e) => {
    if (e.target.classList.contains('quantity') || e.target.classList.contains('unit-price')) {
      const row = e.target.closest('tr');
      if (row) {
        calculateInvoiceLineTotal(row);
      }
    }

    if (e.target.id === 'invoiceTaxRate') {
      calculateInvoiceTotal();
    }
  });
}

// Fonction pour afficher l'aperçu d'une facture
async function showInvoicePreview(invoiceId) {
  try {
    console.log(`Affichage de l'aperçu de la facture ${invoiceId}`);
    const invoice = await invoiceManager.getById(invoiceId);

    if (!invoice) {
      showError('Facture non trouvée');
      return;
    }

    // Générer le contenu HTML de l'aperçu
    const previewContent = `
      <div class="invoice-preview" style="font-size: 16px;">
        <div class="row mb-4">
          <div class="col-md-6">
            <h3 class="text-primary mb-2">GestiCom</h3>
            <p class="mb-1 fs-5">Système de gestion commerciale</p>
            <p class="mb-0 text-muted">Alger, Algérie</p>
          </div>
          <div class="col-md-6 text-end">
            <h3 class="mb-2">FACTURE</h3>
            <p class="mb-1 fs-5"><strong>N° ${invoice.invoiceNumber}</strong></p>
            <p class="mb-1">Date: ${new Date(invoice.date).toLocaleDateString()}</p>
            ${invoice.dueDate ? `<p class="mb-1">Échéance: ${new Date(invoice.dueDate).toLocaleDateString()}</p>` : ''}
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <h5 class="text-primary mb-3">Facturé à:</h5>
            <p class="mb-1 fs-5"><strong>${invoice.clientName}</strong></p>
            ${invoice.clientEmail ? `<p class="mb-1">${invoice.clientEmail}</p>` : ''}
            ${invoice.clientPhone ? `<p class="mb-1">${invoice.clientPhone}</p>` : ''}
            ${invoice.clientAddress ? `<p class="mb-1">${invoice.clientAddress}</p>` : ''}
          </div>
          <div class="col-md-6 text-end">
            <div class="badge ${getBadgeClass(invoice.status)} fs-5 px-3 py-2">${getStatusLabel(invoice.status)}</div>
          </div>
        </div>

        <div class="table-responsive mb-4">
          <table class="table table-bordered" style="font-size: 15px;">
            <thead class="table-light">
              <tr>
                <th style="padding: 12px;">Description</th>
                <th class="text-center" style="padding: 12px;">Quantité</th>
                <th class="text-end" style="padding: 12px;">Prix unitaire</th>
                <th class="text-end" style="padding: 12px;">Total</th>
              </tr>
            </thead>
            <tbody id="previewItemsTable">
              ${invoice.items && invoice.items.length > 0 ?
                invoice.items.map(item => `
                  <tr>
                    <td style="padding: 12px;">
                      <strong>${item.productName}</strong>
                      ${item.productDescription ? `<br><small class="text-muted">${item.productDescription}</small>` : ''}
                      ${item.discountPercent > 0 ? `<br><small class="text-success">Remise: ${item.discountPercent}%</small>` : ''}
                    </td>
                    <td class="text-center" style="padding: 12px;">${item.quantity}</td>
                    <td class="text-end" style="padding: 12px;">${item.unitPrice.toFixed(2)} DA</td>
                    <td class="text-end" style="padding: 12px;"><strong>${item.total.toFixed(2)} DA</strong></td>
                  </tr>
                `).join('') :
                `<tr><td colspan="4" class="text-center text-muted" style="padding: 20px;">Aucun produit dans cette facture</td></tr>`
              }
            </tbody>
          </table>
        </div>

        <div class="row">
          <div class="col-md-6">
            ${invoice.notes ? `
              <h5 class="text-primary mb-3">Notes:</h5>
              <div class="p-3 bg-light border-start border-primary border-4">
                <p class="mb-0" style="font-size: 15px;">${invoice.notes}</p>
              </div>
            ` : ''}
          </div>
          <div class="col-md-6">
            <table class="table table-borderless" style="font-size: 16px;">
              <tr>
                <td class="text-end py-2"><strong>Sous-total HT:</strong></td>
                <td class="text-end py-2 fs-5">${invoice.subtotal.toFixed(2)} DA</td>
              </tr>
              <tr>
                <td class="text-end py-2"><strong>TVA (${invoice.taxRate}%):</strong></td>
                <td class="text-end py-2 fs-5">${invoice.taxAmount.toFixed(2)} DA</td>
              </tr>
              <tr class="table-primary">
                <td class="text-end py-3"><strong style="font-size: 18px;">Total TTC:</strong></td>
                <td class="text-end py-3"><strong style="font-size: 20px; color: #0d6efd;">${invoice.total.toFixed(2)} DA</strong></td>
              </tr>
              ${invoice.amountPaid > 0 ? `
                <tr>
                  <td class="text-end py-2">Montant payé:</td>
                  <td class="text-end py-2 fs-5 text-success">${invoice.amountPaid.toFixed(2)} DA</td>
                </tr>
                <tr class="table-warning">
                  <td class="text-end py-2"><strong>Solde restant:</strong></td>
                  <td class="text-end py-2"><strong class="fs-5 text-warning">${(invoice.total - invoice.amountPaid).toFixed(2)} DA</strong></td>
                </tr>
              ` : ''}
            </table>
          </div>
        </div>
      </div>
    `;

    // Injecter le contenu dans le modal
    document.getElementById('invoicePreviewContent').innerHTML = previewContent;
    document.getElementById('viewInvoiceModalLabel').textContent = `Aperçu - Facture ${invoice.invoiceNumber}`;

    // Stocker l'ID de la facture pour les boutons d'action
    document.getElementById('editFromPreview').setAttribute('data-invoice-id', invoiceId);
    document.getElementById('printFromPreview').setAttribute('data-invoice-id', invoiceId);

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
    modal.show();

  } catch (error) {
    console.error('Erreur lors de l\'affichage de l\'aperçu:', error);
    showError('Erreur lors de l\'affichage de l\'aperçu: ' + error.message);
  }
}

// Fonction pour éditer une facture
async function editInvoice(invoiceId) {
  try {
    console.log(`Édition de la facture ${invoiceId}`);
    const invoice = await invoiceManager.getById(invoiceId);

    if (!invoice) {
      showError('Facture non trouvée');
      return;
    }

    // Remplir le formulaire avec les données de la facture
    document.getElementById('invoiceId').value = invoice.id;

    // Formater la date correctement pour l'input date (YYYY-MM-DD)
    const invoiceDate = new Date(invoice.date);
    const formattedDate = invoiceDate.toISOString().split('T')[0];
    document.getElementById('invoiceDate').value = formattedDate;

    document.getElementById('invoiceClient').value = invoice.clientId;
    document.getElementById('invoiceStatus').value = invoice.status;
    document.getElementById('invoiceNote').value = invoice.notes || '';

    // Vider le tableau des produits existant
    const tableBody = document.getElementById('invoiceItemsTableBody');
    tableBody.innerHTML = '';

    // Charger les lignes de produits de la facture
    if (invoice.items && invoice.items.length > 0) {
      for (const item of invoice.items) {
        await addInvoiceItem();

        // Remplir la ligne avec les données du produit
        const lastRow = tableBody.lastElementChild;
        if (lastRow) {
          const productSelect = lastRow.querySelector('.product-select');
          const quantityInput = lastRow.querySelector('.quantity');
          const priceInput = lastRow.querySelector('.unit-price');
          const totalInput = lastRow.querySelector('.line-total');

          if (productSelect) productSelect.value = item.productId;
          if (quantityInput) quantityInput.value = item.quantity;
          if (priceInput) priceInput.value = item.unitPrice.toFixed(2);
          if (totalInput) totalInput.value = item.total.toFixed(2);
        }
      }
    } else {
      // Ajouter une ligne vide si aucun produit
      await addInvoiceItem();
    }

    // Mettre à jour les totaux
    document.getElementById('invoiceSubtotal').value = invoice.subtotal.toFixed(2) + ' DA';
    document.getElementById('invoiceTax').value = invoice.taxAmount.toFixed(2) + ' DA';
    document.getElementById('invoiceTotal').value = invoice.total.toFixed(2) + ' DA';

    // Changer le titre du modal
    document.getElementById('addInvoiceModalLabel').textContent = `Modifier la facture ${invoice.invoiceNumber}`;

    // Marquer que nous sommes en mode édition
    console.log('✏️ Mode édition activé pour la facture ID:', invoice.id);

    // Ouvrir le modal
    const modal = new bootstrap.Modal(document.getElementById('addInvoiceModal'));
    modal.show();

  } catch (error) {
    console.error('Erreur lors de la récupération de la facture:', error);
    showError('Erreur lors de la récupération de la facture: ' + error.message);
  }
}

// Fonction pour réinitialiser le formulaire de facture
function resetInvoiceForm() {
  console.log('🔄 Réinitialisation du formulaire de facture');

  // Réinitialiser tous les champs
  document.getElementById('invoiceId').value = '';
  document.getElementById('invoiceDate').value = '';
  document.getElementById('invoiceClient').value = '';
  document.getElementById('invoiceStatus').value = 'En attente';
  document.getElementById('invoiceNote').value = '';

  // Vider le tableau des produits
  document.getElementById('invoiceItemsTableBody').innerHTML = '';

  // Réinitialiser les totaux
  document.getElementById('invoiceSubtotal').value = '';
  document.getElementById('invoiceTax').value = '';
  document.getElementById('invoiceTotal').value = '';

  // Remettre le titre par défaut
  document.getElementById('addInvoiceModalLabel').textContent = 'Nouvelle facture';

  // Ajouter une ligne de produit vide
  addInvoiceItem();
}

// Fonction pour obtenir le libellé du statut
function getStatusLabel(status) {
  switch (status) {
    case 'unpaid': return 'Non payée';
    case 'paid': return 'Payée';
    case 'partial': return 'Partiellement payée';
    case 'cancelled': return 'Annulée';
    default: return status;
  }
}

async function printInvoice(invoiceId) {
  try {
    console.log(`Impression de la facture ${invoiceId}`);
    const invoice = await invoiceManager.getById(invoiceId);

    if (!invoice) {
      showError('Facture non trouvée');
      return;
    }

    // Créer une nouvelle fenêtre pour l'impression
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Facture ${invoice.invoiceNumber}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              font-size: 14px;
              margin: 20px;
              color: #333;
              line-height: 1.4;
            }
            .header {
              display: flex;
              justify-content: space-between;
              margin-bottom: 30px;
              border-bottom: 2px solid #007bff;
              padding-bottom: 20px;
            }
            .company-info h1 {
              color: #007bff;
              margin: 0;
              font-size: 24px;
            }
            .invoice-info {
              text-align: right;
            }
            .invoice-info h2 {
              margin: 0;
              font-size: 20px;
            }
            .client-info {
              margin: 30px 0;
            }
            .client-info h3 {
              margin-bottom: 10px;
              color: #007bff;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 20px 0;
            }
            th, td {
              padding: 12px;
              text-align: left;
              border-bottom: 1px solid #ddd;
              font-size: 14px;
            }
            th {
              background-color: #f8f9fa;
              font-weight: bold;
              font-size: 15px;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .totals-table {
              width: 300px;
              margin-left: auto;
              margin-top: 20px;
            }
            .totals-table td {
              border: none;
              padding: 5px 10px;
            }
            .total-row {
              font-weight: bold;
              font-size: 16px;
              background-color: #007bff;
              color: white;
            }
            .status-badge {
              display: inline-block;
              padding: 5px 10px;
              border-radius: 4px;
              font-size: 10px;
              font-weight: bold;
              text-transform: uppercase;
            }
            .status-unpaid { background-color: #dc3545; color: white; }
            .status-paid { background-color: #28a745; color: white; }
            .status-partial { background-color: #ffc107; color: black; }
            .status-cancelled { background-color: #6c757d; color: white; }
            .notes {
              margin-top: 30px;
              padding: 15px;
              background-color: #f8f9fa;
              border-left: 4px solid #007bff;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-info">
              <h1>GestiCom</h1>
              <p>Système de gestion commerciale</p>
              <p>Alger, Algérie</p>
            </div>
            <div class="invoice-info">
              <h2>FACTURE</h2>
              <p><strong>N° ${invoice.invoiceNumber}</strong></p>
              <p>Date: ${new Date(invoice.date).toLocaleDateString()}</p>
              ${invoice.dueDate ? `<p>Échéance: ${new Date(invoice.dueDate).toLocaleDateString()}</p>` : ''}
              <div class="status-badge status-${invoice.status}">${getStatusLabel(invoice.status)}</div>
            </div>
          </div>

          <div class="client-info">
            <h3>Facturé à:</h3>
            <p><strong>${invoice.clientName}</strong></p>
            ${invoice.clientEmail ? `<p>${invoice.clientEmail}</p>` : ''}
            ${invoice.clientPhone ? `<p>${invoice.clientPhone}</p>` : ''}
            ${invoice.clientAddress ? `<p>${invoice.clientAddress}</p>` : ''}
          </div>

          <table>
            <thead>
              <tr>
                <th>Description</th>
                <th class="text-center">Quantité</th>
                <th class="text-right">Prix unitaire</th>
                <th class="text-right">Total</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items && invoice.items.length > 0 ?
                invoice.items.map(item => `
                  <tr>
                    <td>
                      <strong>${item.productName}</strong>
                      ${item.productDescription ? `<br><small style="color: #666;">${item.productDescription}</small>` : ''}
                      ${item.discountPercent > 0 ? `<br><small style="color: #28a745;">Remise: ${item.discountPercent}%</small>` : ''}
                    </td>
                    <td class="text-center">${item.quantity}</td>
                    <td class="text-right">${item.unitPrice.toFixed(2)} DA</td>
                    <td class="text-right"><strong>${item.total.toFixed(2)} DA</strong></td>
                  </tr>
                `).join('') :
                `<tr><td colspan="4" class="text-center" style="padding: 20px; color: #666;">Aucun produit dans cette facture</td></tr>`
              }
            </tbody>
          </table>

          <table class="totals-table">
            <tr>
              <td class="text-right"><strong>Sous-total HT:</strong></td>
              <td class="text-right">${invoice.subtotal.toFixed(2)} DA</td>
            </tr>
            <tr>
              <td class="text-right"><strong>TVA (${invoice.taxRate}%):</strong></td>
              <td class="text-right">${invoice.taxAmount.toFixed(2)} DA</td>
            </tr>
            <tr class="total-row">
              <td class="text-right"><strong>Total TTC:</strong></td>
              <td class="text-right"><strong>${invoice.total.toFixed(2)} DA</strong></td>
            </tr>
            ${invoice.amountPaid > 0 ? `
              <tr>
                <td class="text-right">Montant payé:</td>
                <td class="text-right">${invoice.amountPaid.toFixed(2)} DA</td>
              </tr>
              <tr style="background-color: #ffc107;">
                <td class="text-right"><strong>Solde restant:</strong></td>
                <td class="text-right"><strong>${(invoice.total - invoice.amountPaid).toFixed(2)} DA</strong></td>
              </tr>
            ` : ''}
          </table>

          ${invoice.notes ? `
            <div class="notes">
              <h4>Notes:</h4>
              <p>${invoice.notes}</p>
            </div>
          ` : ''}

          <div class="no-print" style="margin-top: 30px; text-align: center;">
            <button onclick="window.print()" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Imprimer</button>
            <button onclick="window.close()" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Fermer</button>
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();

  } catch (error) {
    console.error('Erreur lors de l\'impression:', error);
    showError('Erreur lors de l\'impression: ' + error.message);
  }
}
