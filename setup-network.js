// Script de configuration pour le réseau local
const os = require('os');
const fs = require('fs');
const path = require('path');

console.log('🔧 Configuration du réseau local pour GestiCom\n');

// Fonction pour obtenir l'adresse IP locale
function getLocalIP() {
  const networkInterfaces = os.networkInterfaces();
  
  for (const interfaceName in networkInterfaces) {
    const interfaces = networkInterfaces[interfaceName];
    for (const iface of interfaces) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost';
}

// Fonction pour vérifier si le port est disponible
function checkPort(port) {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();
    
    server.listen(port, (err) => {
      if (err) {
        resolve(false);
      } else {
        server.once('close', () => resolve(true));
        server.close();
      }
    });
    
    server.on('error', () => resolve(false));
  });
}

// Fonction principale de configuration
async function setupNetwork() {
  const localIP = getLocalIP();
  const port = 3000;
  
  console.log('📍 Informations réseau détectées:');
  console.log(`   - Adresse IP locale: ${localIP}`);
  console.log(`   - Port du serveur: ${port}`);
  console.log(`   - Nom de l'ordinateur: ${os.hostname()}`);
  console.log(`   - Système d'exploitation: ${os.platform()} ${os.arch()}\n`);
  
  // Vérifier si le port est disponible
  const portAvailable = await checkPort(port);
  if (!portAvailable) {
    console.log(`⚠️  Le port ${port} est déjà utilisé. Arrêtez le service qui l'utilise ou changez le port.\n`);
  } else {
    console.log(`✅ Le port ${port} est disponible.\n`);
  }
  
  // Instructions pour les autres appareils
  console.log('🌐 Instructions pour accéder depuis d\'autres appareils du réseau:\n');
  console.log('1. Assurez-vous que tous les appareils sont sur le même réseau WiFi/Ethernet');
  console.log(`2. Sur les autres appareils, ouvrez un navigateur web`);
  console.log(`3. Tapez l'adresse: http://${localIP}:${port}`);
  console.log(`4. L'application GestiCom devrait s'ouvrir\n`);
  
  // Instructions pour le pare-feu Windows
  console.log('🔒 Configuration du pare-feu Windows (si nécessaire):\n');
  console.log('Si les autres appareils ne peuvent pas accéder, exécutez ces commandes en tant qu\'administrateur:');
  console.log(`   netsh advfirewall firewall add rule name="GestiCom" dir=in action=allow protocol=TCP localport=${port}`);
  console.log('   ou désactivez temporairement le pare-feu Windows pour tester\n');
  
  // Créer un fichier de configuration réseau
  const networkConfig = {
    serverIP: localIP,
    serverPort: port,
    accessURL: `http://${localIP}:${port}`,
    hostname: os.hostname(),
    configuredAt: new Date().toISOString()
  };
  
  fs.writeFileSync('network-config.json', JSON.stringify(networkConfig, null, 2));
  console.log('📄 Fichier de configuration réseau créé: network-config.json\n');
  
  // Instructions pour le démarrage
  console.log('🚀 Pour démarrer le serveur en mode réseau:');
  console.log('   1. Exécutez: node server.js');
  console.log('   2. Le serveur affichera les URLs d\'accès');
  console.log('   3. Partagez l\'URL réseau avec les autres utilisateurs\n');
  
  // Conseils de sécurité
  console.log('🔐 Conseils de sécurité:');
  console.log('   - Utilisez ce mode uniquement sur des réseaux de confiance');
  console.log('   - Changez les mots de passe par défaut');
  console.log('   - Considérez l\'ajout d\'une authentification plus robuste');
  console.log('   - Sauvegardez régulièrement la base de données\n');
  
  // Test de connectivité
  console.log('🧪 Test de connectivité:');
  console.log(`   - Testez l'accès local: http://localhost:${port}`);
  console.log(`   - Testez l'accès réseau: http://${localIP}:${port}`);
  console.log(`   - Depuis un autre appareil: http://${localIP}:${port}\n`);
  
  return networkConfig;
}

// Exécuter la configuration si le script est appelé directement
if (require.main === module) {
  setupNetwork().then((config) => {
    console.log('✅ Configuration réseau terminée!');
    console.log('\n📋 Résumé:');
    console.log(`   URL d'accès: ${config.accessURL}`);
    console.log(`   Configuré le: ${new Date(config.configuredAt).toLocaleString()}`);
  }).catch((error) => {
    console.error('❌ Erreur lors de la configuration:', error);
  });
}

module.exports = { setupNetwork, getLocalIP };
