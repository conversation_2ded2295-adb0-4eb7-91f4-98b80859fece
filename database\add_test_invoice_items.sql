-- Script pour ajouter des lignes de produits de test aux factures existantes

-- D'abord, supprimer les lignes existantes pour éviter les doublons
DELETE FROM invoice_items WHERE invoice_id IN (1, 2, 3);

-- Ajouter des lignes pour la facture ID 1 (FACT-001)
INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
(1, 1, 1, 89999.99, 0, 19, 89999.99),  -- 1 Ordinateur portable
(1, 3, 2, 19999.99, 0, 19, 39999.98);  -- 2 Écrans

-- Ajouter des lignes pour la facture ID 2 (FACT-002)  
INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
(2, 2, 2, 49999.99, 0, 19, 99999.98),  -- 2 Smartphones
(2, 4, 3, 2999.99, 0, 19, 8999.97);    -- 3 Souris

-- Ajouter des lignes pour la facture ID 3 (FACT-003)
INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
(3, 1, 2, 89999.99, 0, 19, 179999.98), -- 2 Ordinateurs portables
(3, 2, 1, 49999.99, 0, 19, 49999.99),  -- 1 Smartphone
(3, 3, 1, 19999.99, 0, 19, 19999.99),  -- 1 Écran
(3, 4, 5, 2999.99, 0, 19, 14999.95);   -- 5 Souris

-- Mettre à jour les totaux des factures pour qu'ils correspondent
UPDATE invoices SET 
    subtotal = 129999.97,
    tax_amount = 24699.99,
    total = 154699.96
WHERE id = 1;

UPDATE invoices SET 
    subtotal = 108999.95,
    tax_amount = 20709.99,
    total = 129709.94
WHERE id = 2;

UPDATE invoices SET 
    subtotal = 264999.91,
    tax_amount = 50349.98,
    total = 315349.89
WHERE id = 3;
