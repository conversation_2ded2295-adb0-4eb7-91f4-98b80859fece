# 🌐 Configuration Réseau Local - GestiCom

Ce guide vous explique comment configurer GestiCom pour fonctionner sur un réseau local, permettant à plusieurs utilisateurs d'accéder à l'application depuis différents ordinateurs.

## 🚀 Démarrage Rapide

### Windows
```bash
# Double-cliquez sur le fichier ou exécutez dans le terminal :
start-network.bat
```

### Linux/Mac
```bash
# Rendez le script exécutable et lancez-le :
chmod +x start-network.sh
./start-network.sh
```

### Manuel
```bash
# Configuration du réseau
node setup-network.js

# Démarrage du serveur
node server.js
```

## 📋 Prérequis

1. **Node.js** installé sur l'ordinateur serveur
2. **MySQL** configuré et fonctionnel
3. Tous les appareils sur le **même réseau** (WiFi/Ethernet)
4. **Pare-feu** configuré pour autoriser le port 3000

## 🔧 Configuration Détaillée

### 1. Préparation du Serveur

L'ordinateur qui hébergera l'application (serveur) doit avoir :
- Node.js et MySQL installés
- L'application GestiCom complète
- Une adresse IP fixe (recommandé)

### 2. Configuration du Pare-feu

#### Windows
```cmd
# Ouvrir le port 3000 (en tant qu'administrateur)
netsh advfirewall firewall add rule name="GestiCom" dir=in action=allow protocol=TCP localport=3000
```

#### Linux
```bash
# UFW
sudo ufw allow 3000

# iptables
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
```

### 3. Configuration MySQL (si nécessaire)

Si MySQL refuse les connexions, modifiez `/etc/mysql/mysql.conf.d/mysqld.cnf` :
```ini
bind-address = 0.0.0.0
```

Puis redémarrez MySQL :
```bash
sudo systemctl restart mysql
```

## 🌐 Accès depuis d'Autres Appareils

### Étapes pour les Utilisateurs

1. **Connectez-vous au même réseau** que le serveur
2. **Obtenez l'adresse IP** du serveur (affichée au démarrage)
3. **Ouvrez un navigateur** sur votre appareil
4. **Tapez l'adresse** : `http://IP_DU_SERVEUR:3000`

### Exemple
Si l'IP du serveur est `*************` :
- URL d'accès : `http://*************:3000`

## 🔍 Dépannage

### Problème : "Impossible de se connecter"

1. **Vérifiez le réseau**
   ```bash
   # Testez la connectivité
   ping IP_DU_SERVEUR
   ```

2. **Vérifiez le port**
   ```bash
   # Windows
   netstat -an | findstr :3000
   
   # Linux/Mac
   netstat -an | grep :3000
   ```

3. **Testez le pare-feu**
   - Désactivez temporairement le pare-feu pour tester
   - Si ça fonctionne, ajoutez une règle pour le port 3000

### Problème : "Serveur ne démarre pas"

1. **Port déjà utilisé**
   ```bash
   # Trouvez quel processus utilise le port 3000
   netstat -ano | findstr :3000
   ```

2. **Permissions insuffisantes**
   - Exécutez en tant qu'administrateur (Windows)
   - Utilisez `sudo` si nécessaire (Linux/Mac)

### Problème : "Base de données inaccessible"

1. **Vérifiez MySQL**
   ```bash
   # Statut du service
   systemctl status mysql
   
   # Redémarrage si nécessaire
   systemctl restart mysql
   ```

2. **Testez la connexion**
   ```bash
   mysql -u root -p -h localhost
   ```

## 📱 Appareils Supportés

L'application fonctionne sur tous les appareils avec un navigateur moderne :

- **Ordinateurs** : Windows, Mac, Linux
- **Tablettes** : iPad, Android
- **Smartphones** : iPhone, Android
- **Navigateurs** : Chrome, Firefox, Safari, Edge

## 🔐 Sécurité

### Recommandations

1. **Réseau de confiance uniquement**
   - N'utilisez que sur des réseaux privés sécurisés
   - Évitez les réseaux publics

2. **Mots de passe forts**
   - Changez les mots de passe par défaut
   - Utilisez des mots de passe complexes

3. **Sauvegardes régulières**
   ```bash
   # Sauvegarde MySQL
   mysqldump -u root -p gesticom > backup_$(date +%Y%m%d).sql
   ```

4. **Accès limité**
   - Configurez des comptes utilisateurs séparés
   - Limitez les permissions selon les rôles

## 📊 Monitoring

### Surveillance du Serveur

1. **Logs du serveur**
   - Surveillez la console pour les erreurs
   - Vérifiez les connexions actives

2. **Performance**
   - Surveillez l'utilisation CPU/RAM
   - Vérifiez l'espace disque disponible

3. **Base de données**
   - Surveillez les connexions MySQL
   - Optimisez les requêtes si nécessaire

## 🆘 Support

### Fichiers de Log

Les logs sont affichés dans la console du serveur. Pour les sauvegarder :

```bash
# Rediriger les logs vers un fichier
node server.js > gesticom.log 2>&1
```

### Informations Système

Le fichier `network-config.json` contient les informations de configuration réseau.

### Contact

Pour obtenir de l'aide :
1. Vérifiez ce guide de dépannage
2. Consultez les logs d'erreur
3. Testez avec un seul utilisateur d'abord
4. Vérifiez la configuration réseau

---

## 📝 Notes Importantes

- **Performance** : Plus d'utilisateurs = plus de charge sur le serveur
- **Concurrent** : Testez avec le nombre d'utilisateurs prévu
- **Sauvegarde** : Sauvegardez avant de déployer en réseau
- **Test** : Testez toutes les fonctionnalités en mode réseau

---

*GestiCom - Système de Gestion Commerciale*
