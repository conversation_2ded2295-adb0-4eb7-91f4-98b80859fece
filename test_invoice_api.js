// Script de test pour vérifier l'API des factures
const fetch = require('node-fetch');

async function testInvoiceAPI() {
  try {
    console.log('🔍 Test de récupération de la facture ID 1...');
    
    const response = await fetch('http://localhost:3000/api/invoices/1');
    const invoice = await response.json();
    
    console.log('📄 Facture récupérée:');
    console.log('- ID:', invoice.id);
    console.log('- Numéro:', invoice.invoiceNumber);
    console.log('- Client:', invoice.clientName);
    console.log('- Total:', invoice.total);
    console.log('- Nombre de lignes de produits:', invoice.items ? invoice.items.length : 0);
    
    if (invoice.items && invoice.items.length > 0) {
      console.log('\n📦 Lignes de produits:');
      invoice.items.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.productName} - Qté: ${item.quantity} - Prix: ${item.unitPrice} DA - Total: ${item.total} DA`);
      });
    } else {
      console.log('❌ Aucune ligne de produit trouvée');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
}

testInvoiceAPI();
