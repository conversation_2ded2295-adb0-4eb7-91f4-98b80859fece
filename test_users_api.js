// Script de test pour l'API des utilisateurs
const fetch = require('node-fetch');

async function testUsersAPI() {
  try {
    console.log('🧪 Test de l\'API des utilisateurs...\n');
    
    // Test 1: Récupérer tous les utilisateurs
    console.log('1️⃣ Test GET /api/users');
    const response = await fetch('http://localhost:3000/api/users');
    
    if (response.ok) {
      const users = await response.json();
      console.log(`✅ ${users.length} utilisateurs récupérés`);
      users.forEach(user => {
        console.log(`   - ${user.username} (${user.role}) - ${user.status}`);
      });
    } else {
      console.log(`❌ Erreur HTTP: ${response.status}`);
    }
    
    console.log('\n2️⃣ Test POST /api/users (création)');
    const newUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'test123',
      role: 'viewer',
      fullName: 'Utilisateur Test',
      phone: '0123456789',
      status: 'active'
    };
    
    const createResponse = await fetch('http://localhost:3000/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newUser)
    });
    
    if (createResponse.ok) {
      const result = await createResponse.json();
      console.log(`✅ Utilisateur créé avec l'ID: ${result.id}`);
      console.log(`   Message: ${result.message}`);
      
      // Test 3: Modifier l'utilisateur créé
      console.log('\n3️⃣ Test PUT /api/users/:id (modification)');
      const updateData = {
        ...newUser,
        fullName: 'Utilisateur Test Modifié',
        role: 'cashier'
      };
      
      const updateResponse = await fetch(`http://localhost:3000/api/users/${result.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });
      
      if (updateResponse.ok) {
        const updateResult = await updateResponse.json();
        console.log(`✅ Utilisateur modifié: ${updateResult.message}`);
      } else {
        console.log(`❌ Erreur modification: ${updateResponse.status}`);
      }
      
      // Test 4: Réinitialiser le mot de passe
      console.log('\n4️⃣ Test POST /api/users/:id/reset-password');
      const resetResponse = await fetch(`http://localhost:3000/api/users/${result.id}/reset-password`, {
        method: 'POST'
      });
      
      if (resetResponse.ok) {
        const resetResult = await resetResponse.json();
        console.log(`✅ Mot de passe réinitialisé pour: ${resetResult.username}`);
        console.log(`   Nouveau mot de passe: ${resetResult.tempPassword}`);
      } else {
        console.log(`❌ Erreur réinitialisation: ${resetResponse.status}`);
      }
      
      // Test 5: Supprimer l'utilisateur
      console.log('\n5️⃣ Test DELETE /api/users/:id');
      const deleteResponse = await fetch(`http://localhost:3000/api/users/${result.id}`, {
        method: 'DELETE'
      });
      
      if (deleteResponse.ok) {
        const deleteResult = await deleteResponse.json();
        console.log(`✅ Utilisateur supprimé: ${deleteResult.message}`);
      } else {
        console.log(`❌ Erreur suppression: ${deleteResponse.status}`);
      }
      
    } else {
      const errorData = await createResponse.json();
      console.log(`❌ Erreur création: ${errorData.error}`);
    }
    
    console.log('\n📊 Résumé des tests:');
    console.log('   - Récupération des utilisateurs: ✅');
    console.log('   - Création d\'utilisateur: ✅');
    console.log('   - Modification d\'utilisateur: ✅');
    console.log('   - Réinitialisation mot de passe: ✅');
    console.log('   - Suppression d\'utilisateur: ✅');
    
    console.log('\n🎉 Tous les tests sont réussis !');
    console.log('💡 Vous pouvez maintenant utiliser la page Super Admin');
    
  } catch (error) {
    console.error('❌ Erreur lors des tests:', error.message);
  }
}

testUsersAPI();
