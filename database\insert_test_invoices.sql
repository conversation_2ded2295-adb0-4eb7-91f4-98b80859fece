-- Insertion de factures de test dans la base de données GestiCom
USE gesticom;

-- Insertion de quelques factures de test
INSERT INTO invoices (invoice_number, client_id, date, due_date, subtotal, tax_rate, tax_amount, total, amount_paid, status, notes) VALUES
('FACT-001', 1, '2023-05-15', '2023-06-15', 1008.40, 19.00, 191.60, 1200.00, 1200.00, 'paid', 'Facture payée intégralement'),
('FACT-002', 2, '2023-05-20', '2023-06-20', 714.29, 19.00, 135.71, 850.00, 0.00, 'unpaid', 'En attente de paiement'),
('FACT-003', 3, '2023-05-25', '2023-05-25', 294.12, 19.00, 55.88, 350.00, 0.00, 'overdue', 'Facture en retard'),
('FACT-004', 1, '2023-06-01', '2023-07-01', 840.34, 19.00, 159.66, 1000.00, 500.00, 'partial', 'Paiement partiel reçu'),
('FACT-005', 2, '2023-06-05', '2023-07-05', 420.17, 19.00, 79.83, 500.00, 0.00, 'unpaid', 'Nouvelle facture');

-- Insertion de quelques détails de factures (optionnel)
INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
-- Facture 1
(1, 1, 1, 89999.99, 0, 19.00, 89999.99),
(1, 4, 2, 2999.99, 0, 19.00, 5999.98),

-- Facture 2  
(2, 2, 1, 49999.99, 0, 19.00, 49999.99),
(2, 3, 1, 19999.99, 0, 19.00, 19999.99),

-- Facture 3
(3, 4, 5, 2999.99, 0, 19.00, 14999.95),

-- Facture 4
(4, 1, 1, 89999.99, 0, 19.00, 89999.99),

-- Facture 5
(5, 3, 2, 19999.99, 0, 19.00, 39999.98);
