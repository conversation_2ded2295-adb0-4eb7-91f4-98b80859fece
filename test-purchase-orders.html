<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Bons de Commande - GestiCom</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Custom CSS -->
  <link href="css/style.css" rel="stylesheet">
  <!-- Theme CSS -->
  <link href="css/themes.css" rel="stylesheet">
</head>
<body>
  <div class="container mt-5">
    <div class="row">
      <div class="col-12">
        <h1>Test des Bons de Commande</h1>
        <p>Cette page teste la création et sauvegarde des bons de commande.</p>
        
        <div class="card mb-4">
          <div class="card-header">
            <h5>Test de l'API</h5>
          </div>
          <div class="card-body">
            <button class="btn btn-primary me-2" onclick="testSuppliers()">Tester Fournisseurs</button>
            <button class="btn btn-primary me-2" onclick="testProducts()">Tester Produits</button>
            <button class="btn btn-success me-2" onclick="testCreateOrder()">Créer Bon de Commande Test</button>
            <button class="btn btn-info" onclick="testGetOrders()">Récupérer Bons de Commande</button>
          </div>
        </div>
        
        <div class="card mb-4">
          <div class="card-header">
            <h5>Formulaire de Test</h5>
          </div>
          <div class="card-body">
            <form id="testForm">
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="testDate" class="form-label">Date</label>
                  <input type="date" class="form-control" id="testDate" required>
                </div>
                <div class="col-md-6">
                  <label for="testSupplier" class="form-label">Fournisseur</label>
                  <select class="form-select" id="testSupplier" required>
                    <option value="">Sélectionner un fournisseur</option>
                  </select>
                </div>
              </div>
              
              <div class="row mb-3">
                <div class="col-md-4">
                  <label for="testProduct" class="form-label">Produit</label>
                  <select class="form-select" id="testProduct" required>
                    <option value="">Sélectionner un produit</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <label for="testQuantity" class="form-label">Quantité</label>
                  <input type="number" class="form-control" id="testQuantity" value="1" min="1" required>
                </div>
                <div class="col-md-2">
                  <label for="testPrice" class="form-label">Prix</label>
                  <input type="number" step="0.01" class="form-control" id="testPrice" required>
                </div>
                <div class="col-md-2">
                  <label for="testTotal" class="form-label">Total</label>
                  <input type="text" class="form-control" id="testTotal" readonly>
                </div>
                <div class="col-md-2">
                  <label class="form-label">&nbsp;</label>
                  <button type="button" class="btn btn-success d-block" onclick="calculateTotal()">Calculer</button>
                </div>
              </div>
              
              <div class="mb-3">
                <label for="testNotes" class="form-label">Notes</label>
                <textarea class="form-control" id="testNotes" rows="2"></textarea>
              </div>
              
              <button type="button" class="btn btn-primary" onclick="submitTestForm()">Envoyer Test</button>
            </form>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h5>Logs de Test</h5>
            <button class="btn btn-sm btn-secondary" onclick="clearLogs()">Effacer</button>
          </div>
          <div class="card-body">
            <div id="testLogs" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Configuration -->
  <script src="js/config.js"></script>
  
  <script>
    let suppliers = [];
    let products = [];
    
    function log(message) {
      const logs = document.getElementById('testLogs');
      const timestamp = new Date().toLocaleTimeString();
      logs.innerHTML += `[${timestamp}] ${message}\n`;
      logs.scrollTop = logs.scrollHeight;
      console.log(message);
    }
    
    function clearLogs() {
      document.getElementById('testLogs').innerHTML = '';
    }
    
    async function testSuppliers() {
      try {
        log('🔍 Test récupération des fournisseurs...');
        const response = await window.APP_CONFIG.fetch('/suppliers');
        
        if (response.ok) {
          suppliers = await response.json();
          log(`✅ ${suppliers.length} fournisseurs récupérés`);
          
          // Peupler le select
          const select = document.getElementById('testSupplier');
          select.innerHTML = '<option value="">Sélectionner un fournisseur</option>';
          suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id;
            option.textContent = supplier.name;
            select.appendChild(option);
            log(`📦 Fournisseur: ID=${supplier.id}, Nom=${supplier.name}`);
          });
        } else {
          log(`❌ Erreur: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        log(`❌ Erreur: ${error.message}`);
      }
    }
    
    async function testProducts() {
      try {
        log('🔍 Test récupération des produits...');
        const response = await window.APP_CONFIG.fetch('/products');
        
        if (response.ok) {
          products = await response.json();
          log(`✅ ${products.length} produits récupérés`);
          
          // Peupler le select
          const select = document.getElementById('testProduct');
          select.innerHTML = '<option value="">Sélectionner un produit</option>';
          products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - ${product.price.toFixed(2)} DA`;
            option.dataset.price = product.price;
            select.appendChild(option);
            log(`📦 Produit: ID=${product.id}, Nom=${product.name}, Prix=${product.price}`);
          });
        } else {
          log(`❌ Erreur: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        log(`❌ Erreur: ${error.message}`);
      }
    }
    
    async function testCreateOrder() {
      try {
        log('🛒 Test création bon de commande...');
        
        const testData = {
          date: '2024-01-20',
          supplierId: 1,
          items: [
            {
              productId: 1,
              quantity: 2,
              unitPrice: 89999.99,
              total: 179999.98
            }
          ],
          subtotal: 179999.98,
          taxRate: 19,
          taxAmount: 34199.996,
          total: 214199.976,
          notes: 'Test de création',
          status: 'En attente'
        };
        
        log(`📋 Données de test: ${JSON.stringify(testData, null, 2)}`);
        
        const response = await window.APP_CONFIG.fetch('/purchase-orders', {
          method: 'POST',
          body: JSON.stringify(testData)
        });
        
        log(`📡 Réponse: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
          const result = await response.json();
          log(`✅ Succès: ${JSON.stringify(result, null, 2)}`);
        } else {
          const errorText = await response.text();
          log(`❌ Erreur: ${errorText}`);
        }
      } catch (error) {
        log(`❌ Erreur: ${error.message}`);
      }
    }
    
    async function testGetOrders() {
      try {
        log('📋 Test récupération des bons de commande...');
        const response = await window.APP_CONFIG.fetch('/purchase-orders');
        
        if (response.ok) {
          const orders = await response.json();
          log(`✅ ${orders.length} bons de commande récupérés`);
          orders.forEach(order => {
            log(`📄 BC: ${order.number}, Fournisseur: ${order.supplier}, Montant: ${order.amount} DA`);
          });
        } else {
          log(`❌ Erreur: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        log(`❌ Erreur: ${error.message}`);
      }
    }
    
    function calculateTotal() {
      const quantity = parseFloat(document.getElementById('testQuantity').value) || 0;
      const price = parseFloat(document.getElementById('testPrice').value) || 0;
      const total = quantity * price;
      document.getElementById('testTotal').value = total.toFixed(2);
    }
    
    async function submitTestForm() {
      try {
        log('📝 Soumission du formulaire de test...');
        
        const date = document.getElementById('testDate').value;
        const supplierId = parseInt(document.getElementById('testSupplier').value);
        const productId = parseInt(document.getElementById('testProduct').value);
        const quantity = parseFloat(document.getElementById('testQuantity').value);
        const price = parseFloat(document.getElementById('testPrice').value);
        const total = parseFloat(document.getElementById('testTotal').value);
        const notes = document.getElementById('testNotes').value;
        
        if (!date || !supplierId || !productId || !quantity || !price) {
          log('❌ Veuillez remplir tous les champs obligatoires');
          return;
        }
        
        const formData = {
          date,
          supplierId,
          items: [{
            productId,
            quantity,
            unitPrice: price,
            total
          }],
          subtotal: total,
          taxRate: 19,
          taxAmount: total * 0.19,
          total: total * 1.19,
          notes,
          status: 'En attente'
        };
        
        log(`📋 Données du formulaire: ${JSON.stringify(formData, null, 2)}`);
        
        const response = await window.APP_CONFIG.fetch('/purchase-orders', {
          method: 'POST',
          body: JSON.stringify(formData)
        });
        
        log(`📡 Réponse: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
          const result = await response.json();
          log(`✅ Succès: ${JSON.stringify(result, null, 2)}`);
          alert('Bon de commande créé avec succès !');
        } else {
          const errorText = await response.text();
          log(`❌ Erreur: ${errorText}`);
          alert('Erreur lors de la création');
        }
      } catch (error) {
        log(`❌ Erreur: ${error.message}`);
        alert('Erreur de connexion');
      }
    }
    
    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
      // Définir la date d'aujourd'hui
      document.getElementById('testDate').value = new Date().toISOString().split('T')[0];
      
      // Gestionnaire pour le changement de produit
      document.getElementById('testProduct').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.price) {
          document.getElementById('testPrice').value = selectedOption.dataset.price;
          calculateTotal();
        }
      });
      
      // Gestionnaires pour le calcul automatique
      document.getElementById('testQuantity').addEventListener('input', calculateTotal);
      document.getElementById('testPrice').addEventListener('input', calculateTotal);
      
      log('🚀 Page de test initialisée');
    });
  </script>
</body>
</html>
