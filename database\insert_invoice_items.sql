-- Insertion de lignes de produits pour les factures de test

-- Vérifier d'abord si des factures existent
-- Lignes pour la première facture (ID 1)
INSERT IGNORE INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
(1, 1, 2, 89999.99, 0, 19, 179999.98),  -- 2 Ordinateurs portables
(1, 3, 1, 19999.99, 0, 19, 19999.99);   -- 1 Écran

-- Lignes pour la deuxième facture (ID 2)
INSERT IGNORE INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
(2, 2, 3, 49999.99, 0, 19, 149999.97),  -- 3 Smartphones
(2, 4, 5, 2999.99, 0, 19, 14999.95);    -- 5 Souris

-- Lignes pour la troisième facture (ID 3)
INSERT IGNORE INTO invoice_items (invoice_id, product_id, quantity, unit_price, discount_percent, tax_percent, total) VALUES
(3, 1, 1, 89999.99, 0, 19, 89999.99),   -- 1 Ordinateur portable
(3, 2, 1, 49999.99, 0, 19, 49999.99),   -- 1 Smartphone
(3, 3, 2, 19999.99, 0, 19, 39999.98),   -- 2 Écrans
(3, 4, 3, 2999.99, 0, 19, 8999.97);     -- 3 Souris
