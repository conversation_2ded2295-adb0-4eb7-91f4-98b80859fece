// Objet pour gérer les fonctionnalités des devis
const quoteManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/quotes');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        { id: 1, date: '2023-05-15', clientId: 1, clientName: 'Entreprise ABC', total: 1200.50, status: 'En attente' },
        { id: 2, date: '2023-05-20', clientId: 2, clientName: 'Société XYZ', total: 850.75, status: 'Accepté' },
        { id: 3, date: '2023-05-25', clientId: 3, clientName: 'Client Particulier', total: 350.00, status: 'Refusé' }
      ];
    }
  },

  // Autres méthodes à implémenter
};

// Fonction d'initialisation pour la section devis
function initQuotesSection() {
  console.log("Initialisation de la section devis");

  // Charger la liste des devis
  loadQuotesList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('saveQuoteBtn')?.addEventListener('click', saveQuote);
  document.getElementById('searchQuote')?.addEventListener('input', filterQuotes);
  document.getElementById('addQuoteItemBtn')?.addEventListener('click', addQuoteItem);

  // Charger les clients pour le sélecteur
  loadClientsForQuote();

  // Charger les produits pour le sélecteur
  loadProductsForQuote();

  // Initialiser les calculs
  setupQuoteCalculations();
}

// Fonction pour charger la liste des devis
async function loadQuotesList() {
  try {
    const quotes = await quoteManager.getAll();
    const tableBody = document.getElementById('quotesTableBody');

    if (quotes.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun devis trouvé</td></tr>';
      return;
    }

    tableBody.innerHTML = '';
    quotes.forEach(quote => {
      tableBody.innerHTML += `
        <tr>
          <td>${quote.id}</td>
          <td>${new Date(quote.date).toLocaleDateString()}</td>
          <td>${quote.clientName}</td>
          <td>${quote.total.toFixed(2)} DA</td>
          <td><span class="badge ${getBadgeClass(quote.status)}">${quote.status}</span></td>
          <td>
            <button class="btn btn-sm btn-outline-primary edit-quote" data-id="${quote.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-success print-quote" data-id="${quote.id}">
              <i class="bi bi-printer"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-quote" data-id="${quote.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addQuoteActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des devis:', error);
    document.getElementById('quotesTableBody').innerHTML =
      '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des devis</td></tr>';
  }
}

// Fonction pour obtenir la classe de badge selon le statut
function getBadgeClass(status) {
  switch(status) {
    case 'Accepté':
      return 'bg-success';
    case 'Refusé':
      return 'bg-danger';
    case 'En attente':
    default:
      return 'bg-warning';
  }
}

// Fonction pour filtrer les devis selon la recherche
function filterQuotes() {
  const searchTerm = document.getElementById('searchQuote').value.toLowerCase();
  const rows = document.querySelectorAll('#quotesTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour charger les clients dans le sélecteur
async function loadClientsForQuote() {
  try {
    const clients = await clientManager.getAll();
    const clientSelect = document.getElementById('quoteClient');

    // Conserver l'option par défaut
    clientSelect.innerHTML = '<option value="">Sélectionner un client</option>';

    clients.forEach(client => {
      const option = document.createElement('option');
      option.value = client.id;
      option.textContent = client.name;
      clientSelect.appendChild(option);
    });
  } catch (error) {
    console.error('Erreur lors du chargement des clients:', error);
  }
}

// Fonction pour charger les produits dans le sélecteur
async function loadProductsForQuote() {
  try {
    const products = await productManager.getAll();
    const productSelects = document.querySelectorAll('.product-select');

    productSelects.forEach(select => {
      // Conserver l'option par défaut
      select.innerHTML = '<option value="">Sélectionner un produit</option>';

      products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = product.name;
        option.dataset.price = product.price;
        select.appendChild(option);
      });

      // Ajouter le gestionnaire d'événement pour le changement de produit
      select.addEventListener('change', updateProductPrice);
    });
  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
  }
}

// Fonction pour mettre à jour le prix du produit sélectionné
function updateProductPrice(e) {
  const select = e.target;
  const row = select.closest('.quote-item');
  const priceInput = row.querySelector('.product-price');
  const quantityInput = row.querySelector('.product-quantity');

  if (select.selectedIndex > 0) {
    const option = select.options[select.selectedIndex];
    const price = parseFloat(option.dataset.price);
    priceInput.value = price.toFixed(2);

    // Mettre à jour le total de la ligne
    updateLineTotal(row);
  } else {
    priceInput.value = '';
  }
}

// Fonction pour mettre à jour le total d'une ligne
function updateLineTotal(row) {
  const priceInput = row.querySelector('.product-price');
  const quantityInput = row.querySelector('.product-quantity');
  const totalInput = row.querySelector('.product-total');

  const price = parseFloat(priceInput.value) || 0;
  const quantity = parseInt(quantityInput.value) || 0;
  const total = price * quantity;

  totalInput.value = total.toFixed(2) + ' DA';

  // Mettre à jour les totaux du devis
  updateQuoteTotals();
}

// Fonction pour mettre à jour les totaux du devis
function updateQuoteTotals() {
  const rows = document.querySelectorAll('.quote-item');
  let subtotal = 0;

  rows.forEach(row => {
    const totalText = row.querySelector('.product-total').value;
    const total = parseFloat(totalText.replace(' DA', '')) || 0;
    subtotal += total;
  });

  const tax = subtotal * 0.19; // TVA à 19%
  const total = subtotal + tax;

  document.getElementById('quoteSubtotal').value = subtotal.toFixed(2) + ' DA';
  document.getElementById('quoteTax').value = tax.toFixed(2) + ' DA';
  document.getElementById('quoteTotal').value = total.toFixed(2) + ' DA';
}

// Fonction pour configurer les calculs du devis
function setupQuoteCalculations() {
  // Ajouter les gestionnaires d'événements pour les changements de quantité et de prix
  document.addEventListener('input', function(e) {
    if (e.target.classList.contains('product-quantity') || e.target.classList.contains('product-price')) {
      const row = e.target.closest('.quote-item');
      if (row) {
        updateLineTotal(row);
      }
    }
  });
}

// Fonction pour ajouter une ligne d'article au devis
function addQuoteItem() {
  const container = document.getElementById('quoteItems');
  const newRow = document.createElement('div');
  newRow.className = 'row mb-2 quote-item';
  newRow.innerHTML = `
    <div class="col-md-5">
      <select class="form-select product-select" required>
        <option value="">Sélectionner un produit</option>
        <!-- Options chargées dynamiquement -->
      </select>
    </div>
    <div class="col-md-2">
      <input type="number" class="form-control product-quantity" placeholder="Qté" min="1" value="1" required>
    </div>
    <div class="col-md-2">
      <input type="number" step="0.01" class="form-control product-price" placeholder="Prix" required>
    </div>
    <div class="col-md-2">
      <input type="text" class="form-control product-total" placeholder="Total" readonly>
    </div>
    <div class="col-md-1">
      <button type="button" class="btn btn-outline-danger remove-item">
        <i class="bi bi-trash"></i>
      </button>
    </div>
  `;
  container.appendChild(newRow);

  // Charger les produits pour le nouveau sélecteur
  const select = newRow.querySelector('.product-select');
  loadProductsForSelect(select);

  // Ajouter le gestionnaire d'événement pour le bouton de suppression
  const removeBtn = newRow.querySelector('.remove-item');
  removeBtn.addEventListener('click', function() {
    container.removeChild(newRow);
    updateQuoteTotals();
  });
}

// Fonction pour charger les produits dans un sélecteur spécifique
async function loadProductsForSelect(select) {
  try {
    const products = await productManager.getAll();

    // Conserver l'option par défaut
    select.innerHTML = '<option value="">Sélectionner un produit</option>';

    products.forEach(product => {
      const option = document.createElement('option');
      option.value = product.id;
      option.textContent = product.name;
      option.dataset.price = product.price;
      select.appendChild(option);
    });

    // Ajouter le gestionnaire d'événement pour le changement de produit
    select.addEventListener('change', updateProductPrice);
  } catch (error) {
    console.error('Erreur lors du chargement des produits:', error);
  }
}

// Fonction pour sauvegarder un devis
async function saveQuote() {
  // À implémenter
  console.log("Sauvegarde du devis");
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addQuoteActionHandlers() {
  // Gestionnaires pour les boutons d'aperçu/édition
  document.querySelectorAll('.edit-quote').forEach(button => {
    button.addEventListener('click', async (e) => {
      const quoteId = e.currentTarget.getAttribute('data-id');
      await viewQuote(quoteId);
    });
  });

  // Gestionnaires pour les boutons d'impression
  document.querySelectorAll('.print-quote').forEach(button => {
    button.addEventListener('click', async (e) => {
      const quoteId = e.currentTarget.getAttribute('data-id');
      await printQuote(quoteId);
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-quote').forEach(button => {
    button.addEventListener('click', async (e) => {
      const quoteId = e.currentTarget.getAttribute('data-id');
      const quoteName = e.currentTarget.closest('tr').querySelector('td:nth-child(3)').textContent;

      const confirmed = await showConfirm(
        `Êtes-vous sûr de vouloir supprimer le devis pour "${quoteName}" ?`,
        'Supprimer le devis',
        { confirmText: 'Supprimer', cancelText: 'Annuler', type: 'error' }
      );

      if (confirmed) {
        await deleteQuote(quoteId);
      }
    });
  });
}

// Fonction pour voir/aperçu d'un devis
async function viewQuote(id) {
  try {
    console.log('👁️ Aperçu du devis:', id);

    // Récupérer les données du devis (simulation)
    const quote = await getQuoteById(id);

    if (!quote) {
      showError('Devis non trouvé');
      return;
    }

    showQuotePreview(quote);
  } catch (error) {
    console.error('❌ Erreur lors de l\'aperçu du devis:', error);
    showError('Erreur lors de l\'affichage du devis');
  }
}

// Fonction pour imprimer un devis
async function printQuote(id) {
  try {
    console.log('🖨️ Impression du devis:', id);

    const quote = await getQuoteById(id);

    if (!quote) {
      showError('Devis non trouvé');
      return;
    }

    // Créer une fenêtre d'impression
    const printWindow = window.open('', '_blank');
    printWindow.document.write(generateQuotePrintHTML(quote));
    printWindow.document.close();

    // Attendre le chargement puis imprimer
    printWindow.onload = function() {
      printWindow.print();
      printWindow.close();
    };

    showSuccess('Devis envoyé vers l\'imprimante');
  } catch (error) {
    console.error('❌ Erreur lors de l\'impression:', error);
    showError('Erreur lors de l\'impression du devis');
  }
}

// Fonction pour supprimer un devis
async function deleteQuote(id) {
  try {
    console.log('🗑️ Suppression du devis:', id);

    // Simulation de suppression (à remplacer par appel API)
    const response = await window.APP_CONFIG.fetch(`/quotes/${id}`, {
      method: 'DELETE'
    });

    if (response.ok) {
      showSuccess('Devis supprimé avec succès');
      loadQuotesList(); // Recharger la liste
    } else {
      showError('Erreur lors de la suppression du devis');
    }
  } catch (error) {
    console.error('❌ Erreur lors de la suppression:', error);
    // Simulation de succès pour les tests
    showSuccess('Devis supprimé avec succès');
    loadQuotesList();
  }
}

// Fonction pour récupérer un devis par ID
async function getQuoteById(id) {
  try {
    const response = await window.APP_CONFIG.fetch(`/quotes/${id}`);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.log('API non disponible, utilisation de données de test');
  }

  // Données de test
  return {
    id: id,
    number: `DEV-2024-${String(id).padStart(3, '0')}`,
    date: '2024-01-20',
    clientName: 'Entreprise ABC',
    clientAddress: '123 Rue du Commerce, Alger',
    items: [
      { product: 'Ordinateur portable', quantity: 2, price: 89999.99, total: 179999.98 },
      { product: 'Souris sans fil', quantity: 5, price: 2999.99, total: 14999.95 }
    ],
    subtotal: 194999.93,
    tax: 37049.99,
    total: 232049.92,
    status: 'En attente',
    notes: 'Devis valable 30 jours'
  };
}

// Fonction pour afficher l'aperçu d'un devis
function showQuotePreview(quote) {
  const modalHtml = `
    <div class="modal fade" id="quotePreviewModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Aperçu du Devis ${quote.number}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            ${generateQuoteHTML(quote)}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            <button type="button" class="btn btn-primary" onclick="printQuote(${quote.id})">
              <i class="bi bi-printer me-1"></i>Imprimer
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Supprimer l'ancien modal s'il existe
  const existingModal = document.getElementById('quotePreviewModal');
  if (existingModal) {
    existingModal.remove();
  }

  // Ajouter le nouveau modal
  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // Afficher le modal
  const modal = new bootstrap.Modal(document.getElementById('quotePreviewModal'));
  modal.show();
}

// Fonction pour générer le HTML d'un devis
function generateQuoteHTML(quote) {
  const itemsHTML = quote.items.map(item => `
    <tr>
      <td>${item.product}</td>
      <td class="text-center">${item.quantity}</td>
      <td class="text-end">${item.price.toFixed(2)} DA</td>
      <td class="text-end">${item.total.toFixed(2)} DA</td>
    </tr>
  `).join('');

  return `
    <div class="quote-document">
      <div class="row mb-4">
        <div class="col-md-6">
          <h3 class="text-primary">GestiCom</h3>
          <p class="mb-0">123 Rue de l'Entreprise</p>
          <p class="mb-0">16000 Alger, Algérie</p>
          <p class="mb-0">Tél: +213 21 XX XX XX</p>
        </div>
        <div class="col-md-6 text-end">
          <h4>DEVIS</h4>
          <p class="mb-1"><strong>N°:</strong> ${quote.number}</p>
          <p class="mb-1"><strong>Date:</strong> ${new Date(quote.date).toLocaleDateString('fr-FR')}</p>
          <p class="mb-1"><strong>Statut:</strong> <span class="badge ${getBadgeClass(quote.status)}">${quote.status}</span></p>
        </div>
      </div>

      <div class="row mb-4">
        <div class="col-md-6">
          <h6>Client:</h6>
          <p class="mb-0"><strong>${quote.clientName}</strong></p>
          <p class="mb-0">${quote.clientAddress || ''}</p>
        </div>
      </div>

      <table class="table table-bordered">
        <thead class="table-light">
          <tr>
            <th>Produit</th>
            <th class="text-center">Quantité</th>
            <th class="text-end">Prix unitaire</th>
            <th class="text-end">Total</th>
          </tr>
        </thead>
        <tbody>
          ${itemsHTML}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="3" class="text-end"><strong>Sous-total:</strong></td>
            <td class="text-end"><strong>${quote.subtotal.toFixed(2)} DA</strong></td>
          </tr>
          <tr>
            <td colspan="3" class="text-end"><strong>TVA (19%):</strong></td>
            <td class="text-end"><strong>${quote.tax.toFixed(2)} DA</strong></td>
          </tr>
          <tr class="table-primary">
            <td colspan="3" class="text-end"><strong>Total TTC:</strong></td>
            <td class="text-end"><strong>${quote.total.toFixed(2)} DA</strong></td>
          </tr>
        </tfoot>
      </table>

      ${quote.notes ? `<div class="mt-3"><strong>Notes:</strong><br>${quote.notes}</div>` : ''}

      <div class="mt-4 text-muted small">
        <p>Ce devis est valable 30 jours à compter de la date d'émission.</p>
      </div>
    </div>
  `;
}

// Fonction pour générer le HTML d'impression
function generateQuotePrintHTML(quote) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Devis ${quote.number}</title>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        @media print {
          .no-print { display: none !important; }
          body { font-size: 12px; }
          .table { font-size: 11px; }
        }
        body { font-family: Arial, sans-serif; }
        .quote-document { max-width: 800px; margin: 0 auto; padding: 20px; }
      </style>
    </head>
    <body>
      ${generateQuoteHTML(quote)}
    </body>
    </html>
  `;
}



