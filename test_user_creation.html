<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Création Utilisateur</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
  <div class="container mt-5">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h3>🧪 Test Création Utilisateur</h3>
          </div>
          <div class="card-body">
            
            <!-- Test automatique -->
            <div class="mb-4">
              <h5>Test Automatique</h5>
              <button id="testAutoBtn" class="btn btn-success">Créer Utilisateur Test</button>
              <div id="autoResult" class="mt-2"></div>
            </div>
            
            <hr>
            
            <!-- Formulaire manuel -->
            <h5>Test Manuel</h5>
            <form id="testForm">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="username" class="form-label">Nom d'utilisateur *</label>
                    <input type="text" class="form-control" id="username" value="azed" required>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="email" class="form-label">Email *</label>
                    <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="password" class="form-label">Mot de passe *</label>
                    <input type="password" class="form-control" id="password" value="azed123" required>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="role" class="form-label">Rôle *</label>
                    <select class="form-select" id="role" required>
                      <option value="manager" selected>Gestionnaire</option>
                      <option value="admin">Administrateur</option>
                      <option value="cashier">Caissier</option>
                      <option value="viewer">Consultation</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="fullName" class="form-label">Nom complet</label>
                    <input type="text" class="form-control" id="fullName" value="Azed Gestionnaire">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="phone" class="form-label">Téléphone</label>
                    <input type="tel" class="form-control" id="phone" value="0123456789">
                  </div>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="active" checked>
                  <label class="form-check-label" for="active">
                    Compte actif
                  </label>
                </div>
              </div>
              
              <button type="button" id="submitBtn" class="btn btn-primary">
                🚀 Créer Utilisateur
              </button>
            </form>
            
            <div id="result" class="mt-3"></div>
            
            <hr>
            
            <!-- Liste des utilisateurs -->
            <h5>Utilisateurs Existants</h5>
            <button id="loadUsersBtn" class="btn btn-info mb-3">Charger Liste</button>
            <div id="usersList"></div>
            
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  
  <script>
    console.log('🔧 Page de test chargée');
    
    // Test automatique
    document.getElementById('testAutoBtn').addEventListener('click', async function() {
      console.log('🧪 Test automatique démarré');
      
      const autoResult = document.getElementById('autoResult');
      autoResult.innerHTML = '<div class="text-info">⏳ Création en cours...</div>';
      
      const testData = {
        username: 'auto_test_' + Date.now(),
        email: 'auto_' + Date.now() + '@test.com',
        password: 'test123',
        role: 'viewer',
        fullName: 'Test Automatique',
        phone: '0987654321',
        status: 'active'
      };
      
      try {
        const response = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testData)
        });
        
        console.log('📡 Réponse reçue:', response.status);
        
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        
        const result = await response.json();
        console.log('✅ Succès:', result);
        
        autoResult.innerHTML = `
          <div class="alert alert-success">
            <strong>✅ Succès !</strong><br>
            Utilisateur créé: <strong>${result.username}</strong><br>
            ID: ${result.id}<br>
            Message: ${result.message}
          </div>
        `;
        
      } catch (error) {
        console.error('❌ Erreur:', error);
        autoResult.innerHTML = `
          <div class="alert alert-danger">
            <strong>❌ Erreur !</strong><br>
            ${error.message}
          </div>
        `;
      }
    });
    
    // Test manuel
    document.getElementById('submitBtn').addEventListener('click', async function() {
      console.log('📝 Test manuel démarré');
      
      const result = document.getElementById('result');
      result.innerHTML = '<div class="text-info">⏳ Création en cours...</div>';
      
      const userData = {
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        password: document.getElementById('password').value,
        role: document.getElementById('role').value,
        fullName: document.getElementById('fullName').value,
        phone: document.getElementById('phone').value,
        status: document.getElementById('active').checked ? 'active' : 'inactive'
      };
      
      console.log('📝 Données à envoyer:', userData);
      
      try {
        const response = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });
        
        console.log('📡 Réponse reçue:', response.status);
        
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        
        const resultData = await response.json();
        console.log('✅ Succès:', resultData);
        
        result.innerHTML = `
          <div class="alert alert-success">
            <strong>✅ Utilisateur créé avec succès !</strong><br>
            Nom: <strong>${resultData.username}</strong><br>
            Email: ${resultData.email}<br>
            Rôle: ${resultData.role}<br>
            ID: ${resultData.id}
          </div>
        `;
        
        // Vider le formulaire
        document.getElementById('testForm').reset();
        
      } catch (error) {
        console.error('❌ Erreur:', error);
        result.innerHTML = `
          <div class="alert alert-danger">
            <strong>❌ Erreur lors de la création !</strong><br>
            ${error.message}
          </div>
        `;
      }
    });
    
    // Charger la liste des utilisateurs
    document.getElementById('loadUsersBtn').addEventListener('click', async function() {
      console.log('📋 Chargement de la liste des utilisateurs');
      
      const usersList = document.getElementById('usersList');
      usersList.innerHTML = '<div class="text-info">⏳ Chargement...</div>';
      
      try {
        const response = await fetch('/api/users');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        
        const users = await response.json();
        console.log(`✅ ${users.length} utilisateurs chargés`);
        
        if (users.length === 0) {
          usersList.innerHTML = '<div class="alert alert-info">Aucun utilisateur trouvé</div>';
          return;
        }
        
        const table = `
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Username</th>
                  <th>Email</th>
                  <th>Rôle</th>
                  <th>Statut</th>
                </tr>
              </thead>
              <tbody>
                ${users.map(user => `
                  <tr>
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td><span class="badge bg-info">${user.role}</span></td>
                    <td><span class="badge ${user.status === 'active' ? 'bg-success' : 'bg-danger'}">${user.status}</span></td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        `;
        
        usersList.innerHTML = table;
        
      } catch (error) {
        console.error('❌ Erreur:', error);
        usersList.innerHTML = `
          <div class="alert alert-danger">
            <strong>❌ Erreur lors du chargement !</strong><br>
            ${error.message}
          </div>
        `;
      }
    });
    
    // Charger automatiquement la liste au démarrage
    setTimeout(() => {
      document.getElementById('loadUsersBtn').click();
    }, 1000);
    
  </script>
</body>
</html>
