<div class="container-fluid">
  <!-- En-tête avec statistiques -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="alert alert-warning">
        <i class="bi bi-shield-exclamation me-2"></i>
        <strong>Zone d'Administration Système</strong> - Accès réservé au Super Administrateur
      </div>
    </div>
  </div>

  <!-- Statistiques système -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card text-white bg-primary">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Utilisateurs</h6>
              <h3 id="total-users">0</h3>
            </div>
            <i class="bi bi-people fs-1"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-white bg-success">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Connectés</h6>
              <h3 id="connected-users">0</h3>
            </div>
            <i class="bi bi-wifi fs-1"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-white bg-info">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Serveur</h6>
              <h6 id="server-status">En ligne</h6>
            </div>
            <i class="bi bi-server fs-1"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-white bg-warning">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Réseau</h6>
              <h6 id="network-ip">-</h6>
            </div>
            <i class="bi bi-router fs-1"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Onglets de navigation -->
  <ul class="nav nav-tabs" id="adminTabs" role="tablist">
    <li class="nav-item" role="presentation">
      <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
        <i class="bi bi-people me-2"></i>Gestion Utilisateurs
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="network-tab" data-bs-toggle="tab" data-bs-target="#network" type="button" role="tab">
        <i class="bi bi-router me-2"></i>Configuration Réseau
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
        <i class="bi bi-gear me-2"></i>Paramètres Système
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="monitoring-tab" data-bs-toggle="tab" data-bs-target="#monitoring" type="button" role="tab">
        <i class="bi bi-activity me-2"></i>Monitoring
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
        <i class="bi bi-cloud-download me-2"></i>Sauvegarde
      </button>
    </li>
  </ul>

  <!-- Contenu des onglets -->
  <div class="tab-content" id="adminTabsContent">

    <!-- Onglet Gestion Utilisateurs -->
    <div class="tab-pane fade show active" id="users" role="tabpanel">
      <div class="card mt-3">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5>Gestion des Utilisateurs</h5>
          <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
              <i class="bi bi-person-plus me-2"></i>Ajouter Utilisateur
            </button>
            <button type="button" class="btn btn-success ms-2" onclick="testCreateUser()">
              <i class="bi bi-bug me-2"></i>Test Création
            </button>
            <button type="button" class="btn btn-info ms-2" onclick="loadUsersInline()">
              <i class="bi bi-arrow-clockwise me-2"></i>Actualiser
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Nom d'utilisateur</th>
                  <th>Email</th>
                  <th>Rôle</th>
                  <th>Statut</th>
                  <th>Dernière connexion</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="usersTableBody">
                <!-- Données chargées dynamiquement -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglet Configuration Réseau -->
    <div class="tab-pane fade" id="network" role="tabpanel">
      <div class="card mt-3">
        <div class="card-header">
          <h5>Configuration Réseau</h5>
        </div>
        <div class="card-body">
          <form id="networkConfigForm">
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="serverIP" class="form-label">Adresse IP du Serveur</label>
                <input type="text" class="form-control" id="serverIP" readonly>
                <small class="form-text text-muted">IP détectée automatiquement</small>
              </div>
              <div class="col-md-6">
                <label for="serverPort" class="form-label">Port du Serveur</label>
                <input type="number" class="form-control" id="serverPort" value="3000" min="1000" max="65535">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="maxConnections" class="form-label">Connexions Simultanées Max</label>
                <input type="number" class="form-control" id="maxConnections" value="50" min="1" max="1000">
              </div>
              <div class="col-md-6">
                <label for="sessionTimeout" class="form-label">Timeout Session (minutes)</label>
                <input type="number" class="form-control" id="sessionTimeout" value="60" min="5" max="1440">
              </div>
            </div>
            <div class="mb-3">
              <label for="allowedIPs" class="form-label">IPs Autorisées (optionnel)</label>
              <textarea class="form-control" id="allowedIPs" rows="3" placeholder="***********/24&#10;10.0.0.0/8&#10;Laissez vide pour autoriser toutes les IPs"></textarea>
              <small class="form-text text-muted">Une IP ou plage par ligne. Laissez vide pour autoriser toutes les IPs.</small>
            </div>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle me-2"></i>Sauvegarder Configuration
              </button>
              <button type="button" class="btn btn-info" id="testNetworkBtn">
                <i class="bi bi-wifi me-2"></i>Tester Connectivité
              </button>
              <button type="button" class="btn btn-warning" id="restartServerBtn">
                <i class="bi bi-arrow-clockwise me-2"></i>Redémarrer Serveur
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Informations réseau actuelles -->
      <div class="card mt-3">
        <div class="card-header">
          <h5>Informations Réseau Actuelles</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <strong>URL d'accès local :</strong><br>
              <code id="localURL">http://localhost:3000</code>
              <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('localURL')">
                <i class="bi bi-clipboard"></i>
              </button>
            </div>
            <div class="col-md-6">
              <strong>URL d'accès réseau :</strong><br>
              <code id="networkURL">http://*************:3000</code>
              <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('networkURL')">
                <i class="bi bi-clipboard"></i>
              </button>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <strong>Instructions pour les utilisateurs :</strong>
              <div class="alert alert-info mt-2">
                <i class="bi bi-info-circle me-2"></i>
                Partagez l'URL réseau avec les autres utilisateurs. Ils doivent être connectés au même réseau WiFi/Ethernet.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglet Paramètres Système -->
    <div class="tab-pane fade" id="system" role="tabpanel">
      <div class="card mt-3">
        <div class="card-header">
          <h5>Paramètres Système</h5>
        </div>
        <div class="card-body">
          <form id="systemConfigForm">
            <h6 class="mb-3">Base de Données</h6>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="dbHost" class="form-label">Hôte MySQL</label>
                <input type="text" class="form-control" id="dbHost" value="localhost">
              </div>
              <div class="col-md-6">
                <label for="dbPort" class="form-label">Port MySQL</label>
                <input type="number" class="form-control" id="dbPort" value="3306">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="dbName" class="form-label">Nom de la Base</label>
                <input type="text" class="form-control" id="dbName" value="gesticom">
              </div>
              <div class="col-md-6">
                <label for="dbUser" class="form-label">Utilisateur MySQL</label>
                <input type="text" class="form-control" id="dbUser" value="root">
              </div>
            </div>

            <hr>
            <h6 class="mb-3">Sécurité</h6>
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="enableSSL">
                  <label class="form-check-label" for="enableSSL">
                    Activer HTTPS (SSL)
                  </label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="enableAuth" checked>
                  <label class="form-check-label" for="enableAuth">
                    Authentification obligatoire
                  </label>
                </div>
              </div>
            </div>

            <hr>
            <h6 class="mb-3">Performance</h6>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="cacheTimeout" class="form-label">Cache Timeout (secondes)</label>
                <input type="number" class="form-control" id="cacheTimeout" value="300">
              </div>
              <div class="col-md-6">
                <label for="logLevel" class="form-label">Niveau de Log</label>
                <select class="form-select" id="logLevel">
                  <option value="error">Erreurs seulement</option>
                  <option value="warn">Avertissements</option>
                  <option value="info" selected>Informations</option>
                  <option value="debug">Debug complet</option>
                </select>
              </div>
            </div>

            <button type="submit" class="btn btn-primary">
              <i class="bi bi-check-circle me-2"></i>Sauvegarder Paramètres
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Onglet Monitoring -->
    <div class="tab-pane fade" id="monitoring" role="tabpanel">
      <div class="card mt-3">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5>Monitoring Système</h5>
          <button type="button" class="btn btn-outline-primary" id="refreshMonitoringBtn">
            <i class="bi bi-arrow-clockwise me-2"></i>Actualiser
          </button>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-6">
              <h6>Utilisateurs Connectés</h6>
              <div id="connectedUsersList" class="list-group">
                <!-- Liste dynamique -->
              </div>
            </div>
            <div class="col-md-6">
              <h6>Activité Récente</h6>
              <div id="recentActivityList" class="list-group">
                <!-- Activité dynamique -->
              </div>
            </div>
          </div>

          <h6>Logs Système</h6>
          <div class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto;">
            <pre id="systemLogs">Chargement des logs...</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglet Sauvegarde -->
    <div class="tab-pane fade" id="backup" role="tabpanel">
      <div class="card mt-3">
        <div class="card-header">
          <h5>Sauvegarde et Restauration</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Sauvegarde</h6>
              <p>Créer une sauvegarde complète de la base de données.</p>
              <button type="button" class="btn btn-success" id="createBackupBtn">
                <i class="bi bi-cloud-download me-2"></i>Créer Sauvegarde
              </button>
            </div>
            <div class="col-md-6">
              <h6>Restauration</h6>
              <p>Restaurer la base de données depuis un fichier de sauvegarde.</p>
              <input type="file" class="form-control mb-2" id="backupFile" accept=".sql">
              <button type="button" class="btn btn-warning" id="restoreBackupBtn">
                <i class="bi bi-cloud-upload me-2"></i>Restaurer Sauvegarde
              </button>
            </div>
          </div>

          <hr>
          <h6>Sauvegardes Existantes</h6>
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Taille</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="backupsTableBody">
                <!-- Liste des sauvegardes -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal Ajouter/Modifier Utilisateur -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addUserModalLabel">Ajouter Utilisateur</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <form id="userForm">
          <input type="hidden" id="userId">
          <div class="mb-3">
            <label for="username" class="form-label">Nom d'utilisateur *</label>
            <input type="text" class="form-control" id="username" required>
          </div>
          <div class="mb-3">
            <label for="email" class="form-label">Email *</label>
            <input type="email" class="form-control" id="email" required>
          </div>
          <div class="mb-3">
            <label for="password" class="form-label">Mot de passe *</label>
            <input type="password" class="form-control" id="password" required>
            <small class="form-text text-muted">Minimum 6 caractères</small>
          </div>
          <div class="mb-3">
            <label for="confirmPassword" class="form-label">Confirmer mot de passe *</label>
            <input type="password" class="form-control" id="confirmPassword" required>
          </div>
          <div class="mb-3">
            <label for="userRole" class="form-label">Rôle *</label>
            <select class="form-select" id="userRole" required>
              <option value="">Sélectionner un rôle</option>
              <option value="admin">Administrateur</option>
              <option value="manager">Gestionnaire</option>
              <option value="cashier">Caissier</option>
              <option value="viewer">Consultation</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="fullName" class="form-label">Nom complet</label>
            <input type="text" class="form-control" id="fullName">
          </div>
          <div class="mb-3">
            <label for="phone" class="form-label">Téléphone</label>
            <input type="tel" class="form-control" id="phone">
          </div>
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="userActive" checked>
              <label class="form-check-label" for="userActive">
                Compte actif
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-primary" id="saveUserBtn" onclick="directSaveUser()">Enregistrer</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Confirmation Suppression -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteUserModalLabel">Confirmer la suppression</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <p>Êtes-vous sûr de vouloir supprimer cet utilisateur ?</p>
        <p><strong id="deleteUserName"></strong></p>
        <div class="alert alert-warning">
          <i class="bi bi-exclamation-triangle me-2"></i>
          Cette action est irréversible !
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteUserBtn">Supprimer</button>
      </div>
    </div>
  </div>
</div>

<!-- Styles pour l'animation de chargement -->
<style>
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

<!-- Script intégré pour la gestion des utilisateurs -->
<script>
console.log('🔧 Script Super Admin intégré chargé');

// Fonction directe pour sauvegarder (version simplifiée)
async function directSaveUser() {
  console.log('🎯 directSaveUser() appelée !');

  try {
    const userData = {
      username: document.getElementById('username').value,
      email: document.getElementById('email').value,
      password: document.getElementById('password').value,
      role: document.getElementById('userRole').value,
      fullName: document.getElementById('fullName').value,
      phone: document.getElementById('phone').value,
      status: document.getElementById('userActive').checked ? 'active' : 'inactive'
    };

    console.log('📝 Données:', userData);

    const response = await fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Succès:', result);

    alert('✅ Utilisateur créé: ' + result.username);

    // Fermer le modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
    if (modal) modal.hide();

    // Vider le formulaire
    document.getElementById('userForm').reset();

    // Recharger la liste des utilisateurs (sans recharger la page)
    if (typeof loadUsersInline === 'function') {
      await loadUsersInline();
    } else {
      // Si la fonction n'existe pas, recharger la page
      window.location.reload();
    }

  } catch (error) {
    console.error('❌ Erreur:', error);
    alert('❌ Erreur: ' + error.message);
  }
}

// Fonction pour sauvegarder un utilisateur (version intégrée)
async function handleSaveUser() {
  console.log('🎯 handleSaveUser() appelée !');

  const form = document.getElementById('userForm');
  if (!form) {
    console.error('❌ Formulaire userForm non trouvé');
    alert('Erreur: Formulaire non trouvé');
    return;
  }

  console.log('✅ Formulaire trouvé, validation...');
  if (!form.checkValidity()) {
    console.log('❌ Formulaire invalide');
    form.reportValidity();
    return;
  }

  console.log('✅ Formulaire valide, récupération des données...');

  const userId = document.getElementById('userId').value;
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  // Vérifier les mots de passe
  if (password && password !== confirmPassword) {
    alert('Les mots de passe ne correspondent pas');
    return;
  }

  if (!userId && (!password || password.length < 6)) {
    alert('Le mot de passe doit contenir au moins 6 caractères');
    return;
  }

  const userData = {
    username: document.getElementById('username').value,
    email: document.getElementById('email').value,
    role: document.getElementById('userRole').value,
    fullName: document.getElementById('fullName').value,
    phone: document.getElementById('phone').value,
    status: document.getElementById('userActive').checked ? 'active' : 'inactive'
  };

  if (password && password.trim() !== '') {
    userData.password = password;
  }

  console.log('📝 Données à envoyer:', userData);

  try {
    let response;

    if (userId) {
      // Modification
      console.log('📝 Modification utilisateur ID:', userId);
      response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });
    } else {
      // Création
      console.log('📝 Création nouvel utilisateur:', userData.username);
      response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });
    }

    console.log('📡 Statut de la réponse:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Erreur serveur:', errorText);
      throw new Error(`Erreur HTTP: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Résultat:', result);

    // Afficher le message de succès
    showSuccessMessage(result.message || (userId ? 'Utilisateur modifié avec succès' : 'Utilisateur créé avec succès'));

    // Fermer le modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
    if (modal) {
      modal.hide();
    }

    // Vider le formulaire
    document.getElementById('userForm').reset();

    // Recharger la liste des utilisateurs (sans recharger la page)
    console.log('🔄 Rechargement de la liste...');
    if (typeof loadUsersInline === 'function') {
      await loadUsersInline();
    } else {
      // Si la fonction n'existe pas, recharger la page
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }

  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde:', error);
    showErrorMessage('Erreur lors de la sauvegarde: ' + error.message);
  }
}

// Fonction pour charger les utilisateurs
async function loadUsersInline() {
  const tableBody = document.getElementById('usersTableBody');

  try {
    console.log('🔍 Chargement des utilisateurs...');

    // Afficher un message de chargement
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="bi bi-arrow-clockwise spin"></i> Chargement...</td></tr>';
    }

    const response = await fetch('/api/users');
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    const users = await response.json();
    console.log(`✅ ${users.length} utilisateurs chargés`);

    const tableBody = document.getElementById('usersTableBody');
    if (!tableBody) {
      console.error('❌ Table usersTableBody non trouvée');
      return;
    }

    tableBody.innerHTML = users.map(user => `
      <tr>
        <td>${user.id}</td>
        <td>${user.username}</td>
        <td>${user.email}</td>
        <td>
          <span class="badge bg-info">
            ${user.role}
          </span>
        </td>
        <td>
          <span class="badge ${user.status === 'active' ? 'bg-success' : 'bg-danger'}">
            ${user.status === 'active' ? 'Actif' : 'Inactif'}
          </span>
        </td>
        <td>${user.last_login || 'Jamais'}</td>
        <td>
          <button class="btn btn-sm btn-outline-primary" onclick="editUserInline(${user.id})" title="Modifier">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger" onclick="deleteUserInline(${user.id})" title="Supprimer">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      </tr>
    `).join('');

    // Mettre à jour le compteur
    const totalUsersElement = document.getElementById('total-users');
    if (totalUsersElement) {
      totalUsersElement.textContent = users.length;
    }

  } catch (error) {
    console.error('❌ Erreur lors du chargement des utilisateurs:', error);
    const tableBody = document.getElementById('usersTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Erreur lors du chargement des utilisateurs</td></tr>';
    }
  }
}

// Fonction pour éditer un utilisateur
function editUserInline(userId) {
  console.log('✏️ Édition utilisateur ID:', userId);
  // Pour l'instant, on ouvre juste le modal vide
  // Dans une version complète, on chargerait les données de l'utilisateur
  const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
  modal.show();
}

// Fonction pour supprimer un utilisateur
async function deleteUserInline(userId) {
  try {
    console.log('🗑️ Demande de suppression utilisateur ID:', userId);

    // Récupérer les informations de l'utilisateur
    const userResponse = await fetch(`/api/users`);
    if (!userResponse.ok) {
      throw new Error('Impossible de récupérer les informations utilisateur');
    }

    const users = await userResponse.json();
    const user = users.find(u => u.id == userId);

    if (!user) {
      throw new Error('Utilisateur non trouvé');
    }

    // Créer et afficher le modal de confirmation
    const modalHtml = `
      <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header bg-danger text-white">
              <h5 class="modal-title">
                <i class="bi bi-exclamation-triangle me-2"></i>Confirmer la suppression
              </h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <p>Êtes-vous sûr de vouloir supprimer cet utilisateur ?</p>
              <div class="alert alert-warning">
                <strong>${user.username}</strong><br>
                <small>${user.email}</small>
              </div>
              <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>Cette action est irréversible !</strong>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
              <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                <i class="bi bi-trash me-2"></i>Supprimer
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Supprimer l'ancien modal s'il existe
    const existingModal = document.getElementById('deleteUserModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Ajouter le nouveau modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('deleteUserModal'));
    modal.show();

    // Gestionnaire pour le bouton de confirmation
    document.getElementById('confirmDeleteBtn').onclick = async () => {
      try {
        console.log('🗑️ Confirmation suppression utilisateur ID:', userId);

        // Désactiver le bouton et afficher le chargement
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Suppression...';

        const response = await fetch(`/api/users/${userId}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Erreur HTTP: ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Utilisateur supprimé avec succès');

        // Fermer le modal
        modal.hide();

        // Afficher le message de succès
        showSuccessMessage(result.message || 'Utilisateur supprimé avec succès');

        // Recharger la liste
        await loadUsersInline();

      } catch (error) {
        console.error('❌ Erreur lors de la suppression:', error);
        showErrorMessage('Erreur lors de la suppression: ' + error.message);

        // Restaurer le bouton
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        if (confirmBtn) {
          confirmBtn.disabled = false;
          confirmBtn.innerHTML = '<i class="bi bi-trash me-2"></i>Supprimer';
        }
      }
    };

    // Nettoyer le modal quand il se ferme
    document.getElementById('deleteUserModal').addEventListener('hidden.bs.modal', function () {
      this.remove();
    });

  } catch (error) {
    console.error('❌ Erreur lors de la préparation de la suppression:', error);
    showErrorMessage('Erreur: ' + error.message);
  }
}

// Fonction de test pour créer un utilisateur automatiquement
async function testCreateUser() {
  console.log('🧪 Test de création d\'utilisateur automatique...');

  const testData = {
    username: 'test_' + Date.now(),
    email: 'test_' + Date.now() + '@example.com',
    password: 'test123',
    role: 'viewer',
    fullName: 'Utilisateur Test Auto',
    phone: '0123456789',
    status: 'active'
  };

  try {
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Erreur HTTP: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Test réussi:', result);

    alert(`✅ Test réussi !\nUtilisateur créé: ${result.username}\nID: ${result.id}`);

    // Recharger la liste
    loadUsersInline();

  } catch (error) {
    console.error('❌ Test échoué:', error);
    alert('❌ Test échoué: ' + error.message);
  }
}

// Fonctions utilitaires pour les messages
function showSuccessMessage(message) {
  // Créer une notification de succès
  const alertHtml = `
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <i class="bi bi-check-circle me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  `;

  // Ajouter au début du container
  const container = document.querySelector('#app-container');
  if (container) {
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
      const alert = container.querySelector('.alert-success');
      if (alert) {
        alert.remove();
      }
    }, 5000);
  }
}

function showErrorMessage(message) {
  // Créer une notification d'erreur
  const alertHtml = `
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      <i class="bi bi-exclamation-triangle me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  `;

  // Ajouter au début du container
  const container = document.querySelector('#app-container');
  if (container) {
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // Supprimer automatiquement après 8 secondes
    setTimeout(() => {
      const alert = container.querySelector('.alert-danger');
      if (alert) {
        alert.remove();
      }
    }, 8000);
  }
}

// Charger les utilisateurs au chargement de la page
setTimeout(() => {
  console.log('🚀 Initialisation de la page Super Admin...');
  loadUsersInline();
}, 500);

</script>