// Objet pour gérer les fonctionnalités des retours fournisseurs
const purchaseReturnsManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/purchase-returns');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        {
          id: 1,
          returnNumber: 'RETF-001',
          date: '2024-05-20',
          supplierId: 1,
          supplierName: 'Tech Supplies Inc.',
          originalOrderNumber: 'CMD-001',
          reason: 'Produit défectueux',
          total: 45000.00,
          status: 'pending'
        },
        {
          id: 2,
          returnNumber: 'RETF-002',
          date: '2024-05-22',
          supplierId: 2,
          supplierName: 'Global Electronics',
          originalOrderNumber: 'CMD-002',
          reason: 'Erreur de livraison',
          total: 25000.00,
          status: 'approved'
        }
      ];
    }
  },

  getById: async (id) => {
    try {
      const response = await fetch(`/api/purchase-returns/${id}`);
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de récupération");
      const testReturns = await purchaseReturnsManager.getAll();
      return testReturns.find(returnItem => returnItem.id === parseInt(id)) || null;
    }
  },

  add: async (returnData) => {
    try {
      const response = await fetch('/api/purchase-returns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(returnData)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      return {
        ...returnData,
        id: Math.floor(Math.random() * 1000) + 10,
        returnNumber: `RETF-${String(Math.floor(Math.random() * 1000) + 100).padStart(3, '0')}`
      };
    }
  },

  update: async (id, data) => {
    try {
      const response = await fetch(`/api/purchase-returns/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      return { ...data, id: parseInt(id) };
    }
  },

  delete: async (id) => {
    try {
      await fetch(`/api/purchase-returns/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return true;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  }
};

// Fonction d'initialisation pour la section retours fournisseurs
function initPurchaseReturnsSection() {
  console.log("Initialisation de la section retours fournisseurs");

  // Charger la liste des retours
  loadPurchaseReturnsList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('savePurchaseReturnBtn')?.addEventListener('click', savePurchaseReturn);
  document.getElementById('searchPurchaseReturn')?.addEventListener('input', filterPurchaseReturns);
}

// Fonction pour charger et afficher la liste des retours fournisseurs
async function loadPurchaseReturnsList() {
  try {
    console.log("Chargement de la liste des retours fournisseurs...");
    const returns = await purchaseReturnsManager.getAll();
    const tableBody = document.getElementById('purchaseReturnsTableBody');

    if (!tableBody) {
      console.error("Table des retours fournisseurs non trouvée");
      return;
    }

    tableBody.innerHTML = returns.map(returnItem => `
      <tr>
        <td>${returnItem.returnNumber}</td>
        <td>${new Date(returnItem.date).toLocaleDateString()}</td>
        <td>${returnItem.supplierName}</td>
        <td>${returnItem.originalOrderNumber}</td>
        <td>${returnItem.reason}</td>
        <td>${returnItem.total.toFixed(2)} DA</td>
        <td>
          <span class="badge ${getStatusBadgeClass(returnItem.status)}">
            ${getStatusLabel(returnItem.status)}
          </span>
        </td>
        <td>
          <button class="btn btn-sm btn-outline-info view-return" data-id="${returnItem.id}" title="Aperçu">
            <i class="bi bi-eye"></i>
          </button>
          <button class="btn btn-sm btn-outline-success print-return" data-id="${returnItem.id}" title="Imprimer">
            <i class="bi bi-printer"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger delete-return" data-id="${returnItem.id}" title="Supprimer">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      </tr>
    `).join('');

    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addPurchaseReturnActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des retours fournisseurs:', error);
    const tableBody = document.getElementById('purchaseReturnsTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Erreur lors du chargement des retours fournisseurs</td></tr>';
    }
  }
}

// Fonction pour obtenir la classe CSS du badge de statut
function getStatusBadgeClass(status) {
  switch (status) {
    case 'pending': return 'bg-warning';
    case 'approved': return 'bg-success';
    case 'rejected': return 'bg-danger';
    case 'processed': return 'bg-info';
    default: return 'bg-secondary';
  }
}

// Fonction pour obtenir le libellé du statut
function getStatusLabel(status) {
  switch (status) {
    case 'pending': return 'En attente';
    case 'approved': return 'Approuvé';
    case 'rejected': return 'Rejeté';
    case 'processed': return 'Traité';
    default: return status;
  }
}

// Fonction pour filtrer les retours fournisseurs
function filterPurchaseReturns() {
  const searchTerm = document.getElementById('searchPurchaseReturn')?.value.toLowerCase() || '';
  const rows = document.querySelectorAll('#purchaseReturnsTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour sauvegarder un retour fournisseur
async function savePurchaseReturn() {
  // À implémenter selon les besoins
  console.log("Sauvegarde du retour fournisseur");
  showSuccess('Retour fournisseur sauvegardé avec succès');
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addPurchaseReturnActionHandlers() {
  // Gestionnaires pour les boutons de visualisation
  document.querySelectorAll('.view-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      await viewPurchaseReturn(returnId);
    });
  });

  // Gestionnaires pour les boutons d'impression
  document.querySelectorAll('.print-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      await printPurchaseReturn(returnId);
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      const returnNumber = e.currentTarget.closest('tr').querySelector('td:nth-child(1)').textContent;

      const confirmed = await showConfirm(
        `Êtes-vous sûr de vouloir supprimer le retour fournisseur "${returnNumber}" ?`,
        'Supprimer le retour fournisseur',
        { confirmText: 'Supprimer', cancelText: 'Annuler', type: 'error' }
      );

      if (confirmed) {
        await deletePurchaseReturn(returnId);
      }
    });
  });
}

// Fonction pour afficher un message de succès
function showSuccess(message) {
  // Utiliser la fonction globale si elle existe, sinon console.log
  if (typeof window.showSuccess === 'function') {
    window.showSuccess(message);
  } else {
    console.log('✅ ' + message);
  }
}

// Fonction pour afficher un message d'erreur
function showError(message) {
  // Utiliser la fonction globale si elle existe, sinon console.error
  if (typeof window.showError === 'function') {
    window.showError(message);
  } else {
    console.error('❌ ' + message);
  }
}

// Fonction pour visualiser un retour fournisseur
async function viewPurchaseReturn(returnId) {
  try {
    const returnData = await purchaseReturnsManager.getById(returnId);
    if (!returnData) {
      showError('Retour fournisseur non trouvé');
      return;
    }

    // Créer un modal de visualisation
    const modalHtml = `
      <div class="modal fade" id="viewPurchaseReturnModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="bi bi-eye me-2"></i>Détails du retour ${returnData.returnNumber}
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row mb-3">
                <div class="col-md-6">
                  <strong>Date:</strong> ${new Date(returnData.date).toLocaleDateString()}
                </div>
                <div class="col-md-6">
                  <strong>Fournisseur:</strong> ${returnData.supplierName}
                </div>
              </div>
              <div class="row mb-3">
                <div class="col-md-6">
                  <strong>Commande associée:</strong> ${returnData.originalOrderNumber || 'N/A'}
                </div>
                <div class="col-md-6">
                  <strong>Statut:</strong>
                  <span class="badge ${getStatusBadgeClass(returnData.status)}">
                    ${getStatusLabel(returnData.status)}
                  </span>
                </div>
              </div>
              <div class="mb-3">
                <strong>Motif du retour:</strong><br>
                ${returnData.reason}
              </div>
              <div class="mb-3">
                <strong>Total:</strong> <span class="fs-5 text-primary">${returnData.total.toFixed(2)} DA</span>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
              <button type="button" class="btn btn-success" onclick="printPurchaseReturn(${returnData.id})">
                <i class="bi bi-printer me-2"></i>Imprimer
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Supprimer le modal existant s'il y en a un
    const existingModal = document.getElementById('viewPurchaseReturnModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Ajouter le nouveau modal au DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('viewPurchaseReturnModal'));
    modal.show();

    // Nettoyer le DOM quand le modal est fermé
    document.getElementById('viewPurchaseReturnModal').addEventListener('hidden.bs.modal', function() {
      this.remove();
    });

  } catch (error) {
    console.error('Erreur lors de la visualisation du retour:', error);
    showError('Erreur lors de la visualisation du retour');
  }
}

// Fonction pour imprimer un retour fournisseur
async function printPurchaseReturn(returnId) {
  try {
    console.log('🖨️ Impression du retour fournisseur:', returnId);

    const returnData = await purchaseReturnsManager.getById(returnId);

    if (!returnData) {
      showError('Retour fournisseur non trouvé');
      return;
    }

    // Créer une fenêtre d'impression
    const printWindow = window.open('', '_blank');
    printWindow.document.write(generatePurchaseReturnPrintHTML(returnData));
    printWindow.document.close();

    // Attendre le chargement puis imprimer
    printWindow.onload = function() {
      printWindow.print();
      printWindow.close();
    };

    showSuccess('Retour fournisseur envoyé vers l\'imprimante');
  } catch (error) {
    console.error('❌ Erreur lors de l\'impression:', error);
    showError('Erreur lors de l\'impression du retour fournisseur');
  }
}

// Fonction pour générer le HTML d'impression
function generatePurchaseReturnPrintHTML(returnData) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Retour Fournisseur ${returnData.returnNumber}</title>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        @media print {
          .no-print { display: none !important; }
          body { font-size: 12px; }
          .table { font-size: 11px; }
        }
        body { font-family: Arial, sans-serif; }
        .return-document { max-width: 800px; margin: 0 auto; padding: 20px; }
      </style>
    </head>
    <body>
      <div class="return-document">
        <div class="row mb-4">
          <div class="col-md-6">
            <h3 class="text-primary">GestiCom</h3>
            <p class="mb-0">123 Rue de l'Entreprise</p>
            <p class="mb-0">16000 Alger, Algérie</p>
            <p class="mb-0">Tél: +213 21 XX XX XX</p>
          </div>
          <div class="col-md-6 text-end">
            <h4>RETOUR FOURNISSEUR</h4>
            <p class="mb-1"><strong>N°:</strong> ${returnData.returnNumber}</p>
            <p class="mb-1"><strong>Date:</strong> ${new Date(returnData.date).toLocaleDateString('fr-FR')}</p>
            <p class="mb-1"><strong>Statut:</strong> ${getStatusLabel(returnData.status)}</p>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6">
            <h6>Fournisseur:</h6>
            <p class="mb-0"><strong>${returnData.supplierName}</strong></p>
          </div>
          <div class="col-md-6">
            <h6>Commande associée:</h6>
            <p class="mb-0">${returnData.originalOrderNumber || 'N/A'}</p>
          </div>
        </div>

        <div class="mb-4">
          <h6>Motif du retour:</h6>
          <p>${returnData.reason}</p>
        </div>

        <div class="row mb-4">
          <div class="col-md-6 offset-md-6">
            <div class="border p-3">
              <div class="d-flex justify-content-between mb-2">
                <span><strong>Total du retour:</strong></span>
                <span><strong>${returnData.total.toFixed(2)} DA</strong></span>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-5">
          <div class="col-md-6">
            <div class="border p-3">
              <p class="mb-1"><strong>Signature du fournisseur:</strong></p>
              <div style="height: 60px;"></div>
              <p class="mb-0 text-center">Date et signature</p>
            </div>
          </div>
          <div class="col-md-6">
            <div class="border p-3">
              <p class="mb-1"><strong>Signature du responsable:</strong></p>
              <div style="height: 60px;"></div>
              <p class="mb-0 text-center">Date et signature</p>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Fonction pour supprimer un retour fournisseur
async function deletePurchaseReturn(returnId) {
  try {
    console.log(`🗑️ Suppression du retour fournisseur ${returnId}...`);

    // Appeler la fonction de suppression
    const result = await purchaseReturnsManager.delete(returnId);
    console.log('Résultat de la suppression:', result);

    if (result) {
      // Recharger la liste
      await loadPurchaseReturnsList();

      // Afficher le message de succès
      showSuccess('Retour fournisseur supprimé avec succès');
    } else {
      showError('Erreur lors de la suppression du retour fournisseur');
    }

  } catch (error) {
    console.error('Erreur lors de la suppression du retour fournisseur:', error);
    showError('Erreur lors de la suppression du retour fournisseur: ' + error.message);
  }
}
