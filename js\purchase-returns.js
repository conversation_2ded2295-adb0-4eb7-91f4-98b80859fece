// Objet pour gérer les fonctionnalités des retours fournisseurs
const purchaseReturnsManager = {
  getAll: async () => {
    try {
      const response = await fetch('/api/purchase-returns');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        {
          id: 1,
          returnNumber: 'RETF-001',
          date: '2024-05-20',
          supplierId: 1,
          supplierName: 'Tech Supplies Inc.',
          originalOrderNumber: 'CMD-001',
          reason: 'Produit défectueux',
          total: 45000.00,
          status: 'pending'
        },
        {
          id: 2,
          returnNumber: 'RETF-002',
          date: '2024-05-22',
          supplierId: 2,
          supplierName: 'Global Electronics',
          originalOrderNumber: 'CMD-002',
          reason: 'Erreur de livraison',
          total: 25000.00,
          status: 'approved'
        }
      ];
    }
  },

  getById: async (id) => {
    try {
      const response = await fetch(`/api/purchase-returns/${id}`);
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de récupération");
      const testReturns = await purchaseReturnsManager.getAll();
      return testReturns.find(returnItem => returnItem.id === parseInt(id)) || null;
    }
  },

  add: async (returnData) => {
    try {
      const response = await fetch('/api/purchase-returns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(returnData)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      return {
        ...returnData,
        id: Math.floor(Math.random() * 1000) + 10,
        returnNumber: `RETF-${String(Math.floor(Math.random() * 1000) + 100).padStart(3, '0')}`
      };
    }
  },

  update: async (id, data) => {
    try {
      const response = await fetch(`/api/purchase-returns/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      return { ...data, id: parseInt(id) };
    }
  },

  delete: async (id) => {
    try {
      await fetch(`/api/purchase-returns/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
      });
      return true;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  }
};

// Fonction d'initialisation pour la section retours fournisseurs
function initPurchaseReturnsSection() {
  console.log("Initialisation de la section retours fournisseurs");

  // Charger la liste des retours
  loadPurchaseReturnsList();

  // Ajouter les gestionnaires d'événements
  document.getElementById('savePurchaseReturnBtn')?.addEventListener('click', savePurchaseReturn);
  document.getElementById('searchPurchaseReturn')?.addEventListener('input', filterPurchaseReturns);
}

// Fonction pour charger et afficher la liste des retours fournisseurs
async function loadPurchaseReturnsList() {
  try {
    console.log("Chargement de la liste des retours fournisseurs...");
    const returns = await purchaseReturnsManager.getAll();
    const tableBody = document.getElementById('purchaseReturnsTableBody');

    if (!tableBody) {
      console.error("Table des retours fournisseurs non trouvée");
      return;
    }

    tableBody.innerHTML = returns.map(returnItem => `
      <tr>
        <td>${returnItem.returnNumber}</td>
        <td>${new Date(returnItem.date).toLocaleDateString()}</td>
        <td>${returnItem.supplierName}</td>
        <td>${returnItem.originalOrderNumber}</td>
        <td>${returnItem.reason}</td>
        <td>${returnItem.total.toFixed(2)} DA</td>
        <td>
          <span class="badge ${getStatusBadgeClass(returnItem.status)}">
            ${getStatusLabel(returnItem.status)}
          </span>
        </td>
        <td>
          <button class="btn btn-sm btn-outline-primary view-return" data-id="${returnItem.id}" title="Voir">
            <i class="bi bi-eye"></i>
          </button>
          <button class="btn btn-sm btn-outline-secondary edit-return" data-id="${returnItem.id}" title="Modifier">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger delete-return" data-id="${returnItem.id}" title="Supprimer">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      </tr>
    `).join('');

    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addPurchaseReturnActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des retours fournisseurs:', error);
    const tableBody = document.getElementById('purchaseReturnsTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Erreur lors du chargement des retours fournisseurs</td></tr>';
    }
  }
}

// Fonction pour obtenir la classe CSS du badge de statut
function getStatusBadgeClass(status) {
  switch (status) {
    case 'pending': return 'bg-warning';
    case 'approved': return 'bg-success';
    case 'rejected': return 'bg-danger';
    case 'processed': return 'bg-info';
    default: return 'bg-secondary';
  }
}

// Fonction pour obtenir le libellé du statut
function getStatusLabel(status) {
  switch (status) {
    case 'pending': return 'En attente';
    case 'approved': return 'Approuvé';
    case 'rejected': return 'Rejeté';
    case 'processed': return 'Traité';
    default: return status;
  }
}

// Fonction pour filtrer les retours fournisseurs
function filterPurchaseReturns() {
  const searchTerm = document.getElementById('searchPurchaseReturn')?.value.toLowerCase() || '';
  const rows = document.querySelectorAll('#purchaseReturnsTableBody tr');

  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour sauvegarder un retour fournisseur
async function savePurchaseReturn() {
  // À implémenter selon les besoins
  console.log("Sauvegarde du retour fournisseur");
  showSuccess('Retour fournisseur sauvegardé avec succès');
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addPurchaseReturnActionHandlers() {
  // Gestionnaires pour les boutons de visualisation
  document.querySelectorAll('.view-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      console.log(`Affichage du retour ${returnId}`);
      // À implémenter
    });
  });

  // Gestionnaires pour les boutons d'édition
  document.querySelectorAll('.edit-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      console.log(`Édition du retour ${returnId}`);
      // À implémenter
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-return').forEach(button => {
    button.addEventListener('click', async (e) => {
      const returnId = e.currentTarget.getAttribute('data-id');
      await deletePurchaseReturn(returnId);
    });
  });
}

// Fonction pour afficher un message de succès
function showSuccess(message) {
  // Utiliser la fonction globale si elle existe, sinon console.log
  if (typeof window.showSuccess === 'function') {
    window.showSuccess(message);
  } else {
    console.log('✅ ' + message);
  }
}

// Fonction pour afficher un message d'erreur
function showError(message) {
  // Utiliser la fonction globale si elle existe, sinon console.error
  if (typeof window.showError === 'function') {
    window.showError(message);
  } else {
    console.error('❌ ' + message);
  }
}

// Fonction pour supprimer un retour fournisseur
async function deletePurchaseReturn(returnId) {
  console.log(`Tentative de suppression du retour fournisseur ${returnId}`);

  // Utiliser confirm() simple pour plus de fiabilité
  const confirmed = confirm('Êtes-vous sûr de vouloir supprimer ce retour fournisseur ? Cette action est irréversible.');

  if (!confirmed) {
    console.log('Suppression annulée par l\'utilisateur');
    return;
  }

  try {
    console.log(`Suppression du retour fournisseur ${returnId} en cours...`);

    // Appeler la fonction de suppression
    const result = await purchaseReturnsManager.delete(returnId);
    console.log('Résultat de la suppression:', result);

    // Recharger la liste
    await loadPurchaseReturnsList();

    // Afficher le message de succès
    showSuccess('Retour fournisseur supprimé avec succès');
    console.log('Suppression terminée avec succès');

  } catch (error) {
    console.error('Erreur lors de la suppression du retour fournisseur:', error);
    showError('Erreur lors de la suppression du retour fournisseur: ' + error.message);
  }
}
