// ===== GESTIONNAIRE DE THÈMES =====

class ThemeManager {
  constructor() {
    this.currentTheme = 'dark'; // Mode sombre par défaut
    this.init();
  }

  init() {
    // Charger le thème sauvegardé ou détecter la préférence système
    this.loadSavedTheme();

    // Créer le bouton de basculement (désactivé - utilisation navbar uniquement)
    // this.createThemeToggle();

    // Écouter les changements de préférence système
    this.listenToSystemPreference();

    console.log('🎨 Gestionnaire de thèmes initialisé');
  }

  loadSavedTheme() {
    // Vérifier le thème sauvegardé dans localStorage
    const savedTheme = localStorage.getItem('theme');

    if (savedTheme) {
      this.currentTheme = savedTheme;
    } else {
      // Mode sombre par défaut (au lieu de détecter la préférence système)
      this.currentTheme = 'dark';
    }

    this.applyTheme(this.currentTheme);
    console.log(`🎨 Thème chargé: ${this.currentTheme}`);
  }

  applyTheme(theme) {
    const html = document.documentElement;

    if (theme === 'dark') {
      html.setAttribute('data-theme', 'dark');
      this.currentTheme = 'dark';
    } else {
      html.removeAttribute('data-theme');
      this.currentTheme = 'light';
    }

    // Sauvegarder le choix
    localStorage.setItem('theme', this.currentTheme);

    // Forcer la mise à jour des styles sur tous les éléments
    this.forceStyleUpdate();

    // Corriger les couleurs de texte
    setTimeout(() => {
      if (window.fixTextColors) {
        window.fixTextColors();
      }
    }, 100);

    // Mettre à jour l'icône du bouton
    this.updateToggleIcon();

    // Mettre à jour les boutons de thème si disponibles
    if (window.updateThemeButtons) {
      window.updateThemeButtons();
    } else if (window.updateSidebarThemeButton) {
      window.updateSidebarThemeButton();
    }

    console.log(`🎨 Thème appliqué: ${this.currentTheme}`);
  }

  forceStyleUpdate() {
    // Forcer le recalcul des styles en modifiant temporairement une propriété
    const body = document.body;
    const originalDisplay = body.style.display;
    body.style.display = 'none';

    // Forcer le reflow
    body.offsetHeight;

    // Restaurer
    body.style.display = originalDisplay;

    // Déclencher un événement personnalisé pour notifier le changement de thème
    const event = new CustomEvent('themeChanged', {
      detail: { theme: this.currentTheme }
    });
    document.dispatchEvent(event);
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);

    // Animation du bouton
    const button = document.getElementById('themeToggle');
    if (button) {
      button.style.transform = 'scale(0.8)';
      setTimeout(() => {
        button.style.transform = 'scale(1)';
      }, 150);
    }
  }

  createThemeToggle() {
    // Vérifier si le bouton existe déjà
    if (document.getElementById('themeToggle')) {
      return;
    }

    const button = document.createElement('button');
    button.id = 'themeToggle';
    button.className = 'btn btn-outline-secondary theme-toggle';
    button.title = 'Basculer le thème';
    button.setAttribute('aria-label', 'Basculer entre le mode clair et sombre');

    // Ajouter l'événement de clic
    button.addEventListener('click', () => this.toggleTheme());

    // Ajouter le bouton au body
    document.body.appendChild(button);

    // Mettre à jour l'icône
    this.updateToggleIcon();

    console.log('🎨 Bouton de thème créé');
  }

  updateToggleIcon() {
    const button = document.getElementById('themeToggle');
    if (!button) return;

    if (this.currentTheme === 'dark') {
      button.innerHTML = '<i class="bi bi-sun-fill"></i>';
      button.title = 'Passer au mode clair';
    } else {
      button.innerHTML = '<i class="bi bi-moon-fill"></i>';
      button.title = 'Passer au mode sombre';
    }
  }

  listenToSystemPreference() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      mediaQuery.addEventListener('change', (e) => {
        // Ne changer automatiquement que si l'utilisateur n'a pas de préférence sauvegardée
        if (!localStorage.getItem('theme')) {
          const newTheme = e.matches ? 'dark' : 'light';
          this.applyTheme(newTheme);
          console.log(`🎨 Thème automatique changé: ${newTheme}`);
        }
      });
    }
  }

  // Méthode pour obtenir le thème actuel
  getCurrentTheme() {
    return this.currentTheme;
  }

  // Méthode pour forcer un thème spécifique
  setTheme(theme) {
    if (theme === 'light' || theme === 'dark') {
      this.applyTheme(theme);
    }
  }

  // Méthode pour réinitialiser aux préférences système
  resetToSystemPreference() {
    localStorage.removeItem('theme');

    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      this.applyTheme('dark');
    } else {
      this.applyTheme('light');
    }

    console.log('🎨 Thème réinitialisé aux préférences système');
  }
}

// ===== FONCTIONS UTILITAIRES =====

// Fonction pour ajouter des classes de transition aux éléments
function addThemeTransitions() {
  const elements = document.querySelectorAll('body, .card, .table, .form-control, .btn, .modal-content, .sidebar');
  elements.forEach(el => {
    el.classList.add('theme-transition');
  });
}

// Fonction pour appliquer le thème aux nouveaux éléments dynamiques
function applyThemeToNewElements(container) {
  if (!container) return;

  const elements = container.querySelectorAll('.card, .table, .form-control, .btn, .modal-content');
  elements.forEach(el => {
    el.classList.add('theme-transition');
  });
}

// ===== INITIALISATION =====

// Créer l'instance globale du gestionnaire de thèmes
let themeManager;

// Initialiser quand le DOM est prêt
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    themeManager = new ThemeManager();
    addThemeTransitions();
  });
} else {
  themeManager = new ThemeManager();
  addThemeTransitions();
}

// Écouter les changements de thème pour forcer la mise à jour
document.addEventListener('themeChanged', function(event) {
  console.log('🎨 Événement de changement de thème détecté:', event.detail.theme);

  // Forcer la mise à jour de tous les éléments visibles
  setTimeout(() => {
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      // Forcer le recalcul des styles en touchant une propriété
      const computedStyle = window.getComputedStyle(element);
      element.style.color = computedStyle.color;
    });
  }, 50);
});

// Fonction pour corriger les couleurs selon le thème
function fixTextColors() {
  const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

  if (isDarkMode) {
    // Mode sombre : forcer le texte blanc
    const allTextElements = document.querySelectorAll('*:not(.btn):not(.badge):not(.alert)');
    allTextElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      if (computedStyle.color === 'rgb(0, 0, 0)' || computedStyle.color === '#000000') {
        element.style.color = '#ffffff';
      }
    });
  } else {
    // Mode clair : restaurer les couleurs par défaut
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      // Supprimer les styles inline de couleur pour laisser CSS prendre le contrôle
      if (element.style.color) {
        element.style.removeProperty('color');
      }
    });
  }
}

// Exporter pour utilisation globale
window.themeManager = themeManager;
window.applyThemeToNewElements = applyThemeToNewElements;
window.fixTextColors = fixTextColors;

console.log('🎨 Module de thèmes chargé');
