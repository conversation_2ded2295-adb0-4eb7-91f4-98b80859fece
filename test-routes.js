// Script pour tester les routes du serveur
const express = require('express');

// Créer une application Express temporaire pour lister les routes
const app = express();

// Charger le serveur principal
require('./server.js');

// Fonction pour lister toutes les routes
function listRoutes() {
  console.log('\n📋 Routes disponibles:');
  console.log('='.repeat(50));
  
  app._router.stack.forEach((middleware) => {
    if (middleware.route) {
      // Route directe
      const methods = Object.keys(middleware.route.methods).join(', ').toUpperCase();
      console.log(`${methods.padEnd(10)} ${middleware.route.path}`);
    } else if (middleware.name === 'router') {
      // Router middleware
      middleware.handle.stack.forEach((handler) => {
        if (handler.route) {
          const methods = Object.keys(handler.route.methods).join(', ').toUpperCase();
          console.log(`${methods.padEnd(10)} ${handler.route.path}`);
        }
      });
    }
  });
  
  console.log('='.repeat(50));
}

// Attendre un peu puis lister les routes
setTimeout(() => {
  try {
    listRoutes();
  } catch (error) {
    console.error('Erreur lors du listage des routes:', error.message);
  }
  process.exit(0);
}, 2000);
