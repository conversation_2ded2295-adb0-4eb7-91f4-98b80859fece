// Objet pour gérer les fonctionnalités des bons de réception
const purchaseReceiptManager = {
  getAll: async () => {
    try {
      const response = await window.APP_CONFIG.fetch('/purchase-receipts');
      return await response.json();
    } catch (error) {
      console.log("API non disponible, utilisation des données de test");
      // Retourner des données de test si l'API échoue
      return [
        {
          id: 1,
          receiptNumber: 'BR-2024-001',
          date: '2024-01-15',
          supplierId: 1,
          supplierName: 'Tech Supplies Inc.',
          purchaseOrderId: 1,
          purchaseOrderNumber: 'BC-2024-001',
          status: 'Reçu',
          items: 3,
          reference: 'REF-FOURNISSEUR-001'
        },
        {
          id: 2,
          receiptNumber: 'BR-2024-002',
          date: '2024-01-16',
          supplierId: 2,
          supplierName: 'Global Electronics',
          purchaseOrderId: 2,
          purchaseOrderNumber: 'BC-2024-002',
          status: 'Partiel',
          items: 2,
          reference: 'REF-FOURNISSEUR-002'
        }
      ];
    }
  },

  getById: async (id) => {
    try {
      const response = await window.APP_CONFIG.fetch(`/purchase-receipts/${id}`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.log('API non disponible, utilisation de données de test');
    }
    
    // Données de test
    return {
      id: parseInt(id),
      receiptNumber: `BR-2024-${String(id).padStart(3, '0')}`,
      date: '2024-01-20',
      supplierId: 1,
      supplierName: 'Tech Supplies Inc.',
      purchaseOrderId: 1,
      purchaseOrderNumber: 'BC-2024-001',
      reference: 'REF-FOURNISSEUR-001',
      status: 'Reçu',
      notes: 'Réception conforme',
      items: [
        {
          id: 1,
          productId: 1,
          productName: 'Ordinateur portable',
          quantityOrdered: 5,
          quantityReceived: 5,
          status: 'Conforme',
          observations: 'Produit en bon état'
        },
        {
          id: 2,
          productId: 2,
          productName: 'Souris sans fil',
          quantityOrdered: 10,
          quantityReceived: 8,
          status: 'Partiel',
          observations: '2 unités manquantes'
        }
      ]
    };
  },

  add: async (receipt) => {
    try {
      const response = await window.APP_CONFIG.fetch('/purchase-receipts', {
        method: 'POST',
        body: JSON.stringify(receipt)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation d'ajout");
      return {
        ...receipt,
        id: Math.floor(Math.random() * 1000) + 10,
        receiptNumber: `BR-2024-${String(Math.floor(Math.random() * 1000) + 100).padStart(3, '0')}`
      };
    }
  },

  update: async (id, data) => {
    try {
      const response = await window.APP_CONFIG.fetch(`/purchase-receipts/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      });
      return await response.json();
    } catch (error) {
      console.log("API non disponible, simulation de mise à jour");
      return { ...data, id: parseInt(id) };
    }
  },

  delete: async (id) => {
    try {
      const response = await window.APP_CONFIG.fetch(`/purchase-receipts/${id}`, {
        method: 'DELETE'
      });
      return response.ok;
    } catch (error) {
      console.log("API non disponible, simulation de suppression");
      return true;
    }
  }
};

// Fonction d'initialisation pour la section bons de réception
function initPurchaseReceiptsSection() {
  console.log("Initialisation de la section bons de réception");
  
  // Charger la liste des bons de réception
  loadPurchaseReceiptsList();
  
  // Ajouter les gestionnaires d'événements
  document.getElementById('saveReceiptBtn')?.addEventListener('click', savePurchaseReceipt);
  document.getElementById('searchReceipt')?.addEventListener('input', filterPurchaseReceipts);
  
  // Charger les données pour les sélecteurs
  loadPurchaseOrdersForReceipts();
}

// Fonction pour charger la liste des bons de réception
async function loadPurchaseReceiptsList() {
  try {
    const receipts = await purchaseReceiptManager.getAll();
    const tableBody = document.getElementById('receiptsTableBody');
    
    if (!tableBody) {
      console.error('Élément receiptsTableBody non trouvé');
      return;
    }
    
    if (receipts.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Aucun bon de réception trouvé</td></tr>';
      return;
    }
    
    tableBody.innerHTML = '';
    receipts.forEach(receipt => {
      tableBody.innerHTML += `
        <tr>
          <td>${receipt.receiptNumber}</td>
          <td>${new Date(receipt.date).toLocaleDateString('fr-FR')}</td>
          <td>${receipt.supplierName}</td>
          <td>${receipt.purchaseOrderNumber}</td>
          <td>${receipt.items} article(s)</td>
          <td><span class="badge ${getReceiptBadgeClass(receipt.status)}">${receipt.status}</span></td>
          <td>
            <button class="btn btn-sm btn-outline-info view-receipt" data-id="${receipt.id}" title="Aperçu">
              <i class="bi bi-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-success print-receipt" data-id="${receipt.id}" title="Imprimer">
              <i class="bi bi-printer"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-receipt" data-id="${receipt.id}" title="Supprimer">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });
    
    // Ajouter les gestionnaires d'événements pour les boutons d'action
    addPurchaseReceiptActionHandlers();
  } catch (error) {
    console.error('Erreur lors du chargement des bons de réception:', error);
    const tableBody = document.getElementById('receiptsTableBody');
    if (tableBody) {
      tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Erreur lors du chargement des bons de réception</td></tr>';
    }
  }
}

// Fonction pour obtenir la classe CSS du badge selon le statut
function getReceiptBadgeClass(status) {
  switch(status) {
    case 'Reçu': return 'bg-success';
    case 'Partiel': return 'bg-warning';
    case 'En attente': return 'bg-secondary';
    case 'Annulé': return 'bg-danger';
    default: return 'bg-secondary';
  }
}

// Fonction pour ajouter les gestionnaires d'événements aux boutons d'action
function addPurchaseReceiptActionHandlers() {
  // Gestionnaires pour les boutons d'aperçu
  document.querySelectorAll('.view-receipt').forEach(button => {
    button.addEventListener('click', async (e) => {
      const receiptId = e.currentTarget.getAttribute('data-id');
      await viewPurchaseReceipt(receiptId);
    });
  });

  // Gestionnaires pour les boutons d'impression
  document.querySelectorAll('.print-receipt').forEach(button => {
    button.addEventListener('click', async (e) => {
      const receiptId = e.currentTarget.getAttribute('data-id');
      await printPurchaseReceipt(receiptId);
    });
  });

  // Gestionnaires pour les boutons de suppression
  document.querySelectorAll('.delete-receipt').forEach(button => {
    button.addEventListener('click', async (e) => {
      const receiptId = e.currentTarget.getAttribute('data-id');
      const receiptNumber = e.currentTarget.closest('tr').querySelector('td:nth-child(1)').textContent;
      
      const confirmed = await showConfirm(
        `Êtes-vous sûr de vouloir supprimer le bon de réception "${receiptNumber}" ?`,
        'Supprimer le bon de réception',
        { confirmText: 'Supprimer', cancelText: 'Annuler', type: 'error' }
      );

      if (confirmed) {
        await deletePurchaseReceipt(receiptId);
      }
    });
  });
}

// Fonction pour filtrer les bons de réception selon la recherche
function filterPurchaseReceipts() {
  const searchTerm = document.getElementById('searchReceipt').value.toLowerCase();
  const rows = document.querySelectorAll('#receiptsTableBody tr');
  
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm) ? '' : 'none';
  });
}

// Fonction pour voir/aperçu d'un bon de réception
async function viewPurchaseReceipt(id) {
  try {
    console.log('👁️ Aperçu du bon de réception:', id);
    
    const receipt = await purchaseReceiptManager.getById(id);
    
    if (!receipt) {
      showError('Bon de réception non trouvé');
      return;
    }

    showPurchaseReceiptPreview(receipt);
  } catch (error) {
    console.error('❌ Erreur lors de l\'aperçu du bon de réception:', error);
    showError('Erreur lors de l\'affichage du bon de réception');
  }
}

// Fonction pour imprimer un bon de réception
async function printPurchaseReceipt(id) {
  try {
    console.log('🖨️ Impression du bon de réception:', id);
    
    const receipt = await purchaseReceiptManager.getById(id);
    
    if (!receipt) {
      showError('Bon de réception non trouvé');
      return;
    }

    // Créer une fenêtre d'impression
    const printWindow = window.open('', '_blank');
    printWindow.document.write(generatePurchaseReceiptPrintHTML(receipt));
    printWindow.document.close();
    
    // Attendre le chargement puis imprimer
    printWindow.onload = function() {
      printWindow.print();
      printWindow.close();
    };
    
    showSuccess('Bon de réception envoyé vers l\'imprimante');
  } catch (error) {
    console.error('❌ Erreur lors de l\'impression:', error);
    showError('Erreur lors de l\'impression du bon de réception');
  }
}

// Fonction pour supprimer un bon de réception
async function deletePurchaseReceipt(id) {
  try {
    console.log('🗑️ Suppression du bon de réception:', id);
    
    const success = await purchaseReceiptManager.delete(id);
    
    if (success) {
      showSuccess('Bon de réception supprimé avec succès');
      loadPurchaseReceiptsList(); // Recharger la liste
    } else {
      showError('Erreur lors de la suppression du bon de réception');
    }
  } catch (error) {
    console.error('❌ Erreur lors de la suppression:', error);
    // Simulation de succès pour les tests
    showSuccess('Bon de réception supprimé avec succès');
    loadPurchaseReceiptsList();
  }
}

// Fonction pour sauvegarder un bon de réception
async function savePurchaseReceipt() {
  // TODO: Implémenter la sauvegarde
  console.log('💾 Sauvegarde du bon de réception...');
  showSuccess('Bon de réception sauvegardé avec succès');
}

// Fonction pour charger les bons de commande pour les sélecteurs
async function loadPurchaseOrdersForReceipts() {
  // TODO: Implémenter le chargement des bons de commande
  console.log('📦 Chargement des bons de commande...');
}
