# Configuration MySQL pour GestiCom

## Étapes de configuration

### 1. Configuration de la base de données

Modifiez le fichier `config/mysql.js` avec vos paramètres MySQL :

```javascript
const dbConfig = {
  host: 'localhost',        // Adresse de votre serveur MySQL
  user: 'root',            // Votre nom d'utilisateur MySQL
  password: '',            // Votre mot de passe MySQL (MODIFIEZ ICI)
  database: 'gesticom',    // Nom de la base de données
  port: 3306,              // Port MySQL (généralement 3306)
  charset: 'utf8mb4'
};
```

### 2. Création de la base de données

1. **Ouvrez phpMyAdmin** ou votre client MySQL
2. **Exécutez le fichier SQL** : `database/gesticom_mysql.sql`
3. **Ou copiez-collez** le contenu du fichier dans l'interface SQL

### 3. Vérification de la connexion

Après avoir configuré la base de données :

1. **Red<PERSON><PERSON>rez le serveur** : `npm start`
2. **Vérifiez les logs** : Vous devriez voir "✅ Connexion à la base de données MySQL réussie"
3. **Testez l'application** : Ouvrez http://localhost:3000

### 4. Données de test incluses

La base de données sera créée avec :

- **Utilisateur admin** : `admin` / `admin`
- **4 catégories** de produits
- **4 produits** d'exemple
- **3 clients** de test
- **3 fournisseurs** de test

### 5. Résolution des problèmes

#### Erreur de connexion MySQL

Si vous voyez "❌ Erreur de connexion à la base de données" :

1. **Vérifiez que MySQL est démarré**
2. **Vérifiez les paramètres** dans `config/mysql.js`
3. **Vérifiez les permissions** de l'utilisateur MySQL
4. **Vérifiez que la base de données existe**

#### Erreur "Access denied"

```bash
# Connectez-vous à MySQL et créez un utilisateur si nécessaire
mysql -u root -p
CREATE USER 'gesticom'@'localhost' IDENTIFIED BY 'motdepasse';
GRANT ALL PRIVILEGES ON gesticom.* TO 'gesticom'@'localhost';
FLUSH PRIVILEGES;
```

#### Erreur "Database does not exist"

```sql
-- Créez la base de données manuellement
CREATE DATABASE gesticom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 6. Configuration avancée

Pour un environnement de production, modifiez :

```javascript
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'gesticom',
  port: process.env.DB_PORT || 3306,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};
```

### 7. Test de l'API

Une fois configuré, testez les endpoints :

- **Produits** : http://localhost:3000/api/products
- **Clients** : http://localhost:3000/api/clients
- **Fournisseurs** : http://localhost:3000/api/suppliers
- **Factures** : http://localhost:3000/api/invoices

## Fonctionnalités maintenant connectées

✅ **Produits** : Récupération depuis la table `products`
✅ **Clients** : Récupération depuis la table `clients`
✅ **Fournisseurs** : Récupération depuis la table `suppliers`
✅ **Factures** : Récupération depuis la table `invoices`
✅ **Catégories** : Jointure avec la table `categories`

## Prochaines étapes

Pour compléter l'intégration :

1. **Ajouter les routes POST/PUT/DELETE** pour la création/modification/suppression
2. **Implémenter l'authentification** avec la table `users`
3. **Ajouter la gestion des stocks** avec `inventory_movements`
4. **Implémenter les devis** avec la table `quotes`
