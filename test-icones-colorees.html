<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Icônes Colorées - GestiCom</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Custom CSS -->
  <link href="css/style.css" rel="stylesheet">
  <!-- Theme CSS -->
  <link href="css/themes.css" rel="stylesheet">
  
  <!-- Script pour appliquer le thème immédiatement -->
  <script>
    (function() {
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        localStorage.setItem('theme', 'light');
        document.documentElement.removeAttribute('data-theme');
      } else if (savedTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
      }
    })();
  </script>
</head>
<body>
  <!-- Bouton de thème flottant -->
  <button id="themeToggle" class="btn btn-outline-secondary" style="position: fixed; top: 20px; right: 20px; z-index: 1050; border-radius: 50%; width: 50px; height: 50px;" title="Basculer le thème">
    <i class="bi bi-moon-fill"></i>
  </button>

  <div class="container mt-5">
    <div class="row">
      <div class="col-12">
        <h1>Test des Icônes Colorées - GestiCom</h1>
        <p>Cette page teste la colorisation des icônes en mode clair et sombre.</p>
        
        <div class="card mb-4">
          <div class="card-header">
            <h5>Navbar - Icônes principales</h5>
          </div>
          <div class="card-body">
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-shop me-2 fs-2"></i>
              <span>Logo GestiCom (Bleu)</span>
            </div>
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-person-circle me-2"></i>
              <span>Utilisateur (Gris)</span>
            </div>
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-moon-fill me-2"></i>
              <span>Thème (Jaune)</span>
            </div>
            <div class="d-flex align-items-center">
              <i class="bi bi-box-arrow-right me-2"></i>
              <span>Déconnexion (Rouge)</span>
            </div>
          </div>
        </div>
        
        <div class="card mb-4">
          <div class="card-header">
            <h5>Sidebar - Icônes de navigation</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="nav-link mb-2" data-section="dashboard">
                  <i class="bi bi-speedometer2 me-2"></i>Tableau de bord (Bleu)
                </div>
                <div class="nav-link mb-2" data-section="products">
                  <i class="bi bi-box me-2"></i>Produits (Orange)
                </div>
                <div class="nav-link mb-2" data-section="clients">
                  <i class="bi bi-people me-2"></i>Clients (Teal)
                </div>
                <div class="nav-link mb-2" data-section="suppliers">
                  <i class="bi bi-truck me-2"></i>Fournisseurs (Violet)
                </div>
                <div class="nav-link mb-2" data-section="pos">
                  <i class="bi bi-calculator me-2"></i>Caisse (Vert)
                </div>
                <div class="nav-link mb-2" data-section="quotes">
                  <i class="bi bi-file-earmark-text me-2"></i>Devis (Cyan)
                </div>
              </div>
              <div class="col-md-6">
                <div class="nav-link mb-2" data-section="purchase-orders">
                  <i class="bi bi-cart me-2"></i>Bons de commande (Rose)
                </div>
                <div class="nav-link mb-2" data-section="invoices">
                  <i class="bi bi-receipt me-2"></i>Factures (Rouge)
                </div>
                <div class="nav-link mb-2" data-section="delivery">
                  <i class="bi bi-truck me-2"></i>Livraisons (Indigo)
                </div>
                <div class="nav-link mb-2" data-section="inventory">
                  <i class="bi bi-clipboard-check me-2"></i>Inventaire (Orange)
                </div>
                <div class="nav-link mb-2" data-section="sales-returns">
                  <i class="bi bi-arrow-return-left me-2"></i>Retours clients (Jaune)
                </div>
                <div class="nav-link mb-2" data-section="purchase-returns">
                  <i class="bi bi-arrow-return-right me-2"></i>Retours fournisseurs (Gris)
                </div>
                <div class="nav-link mb-2" data-section="settings">
                  <i class="bi bi-gear me-2"></i>Paramètres (Gris foncé)
                </div>
                <div class="nav-link mb-2" data-section="super-admin">
                  <i class="bi bi-shield-exclamation me-2"></i>Super Admin (Rouge)
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h5>Instructions</h5>
          </div>
          <div class="card-body">
            <p><strong>Mode clair :</strong> Toutes les icônes doivent être colorées selon leur fonction</p>
            <p><strong>Mode sombre :</strong> Toutes les icônes doivent être blanches pour la lisibilité</p>
            <p><strong>Effets hover :</strong> Les icônes doivent grossir légèrement et changer de couleur au survol</p>
            <button class="btn btn-primary" onclick="toggleTheme()">Basculer le thème pour tester</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Theme JS -->
  <script src="js/theme.js"></script>
  
  <script>
    function toggleTheme() {
      if (window.themeManager) {
        window.themeManager.toggleTheme();
        updateThemeButton();
      }
    }
    
    function updateThemeButton() {
      const button = document.getElementById('themeToggle');
      const icon = button.querySelector('i');
      const currentTheme = localStorage.getItem('theme');
      
      if (currentTheme === 'dark') {
        icon.className = 'bi bi-sun-fill';
        button.title = 'Passer au mode clair';
      } else {
        icon.className = 'bi bi-moon-fill';
        button.title = 'Passer au mode sombre';
      }
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      const themeToggle = document.getElementById('themeToggle');
      themeToggle.addEventListener('click', toggleTheme);
      
      setTimeout(updateThemeButton, 500);
    });
  </script>
</body>
</html>
