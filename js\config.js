// Configuration dynamique pour l'accès réseau
console.log('🔧 Configuration réseau chargée');

// Détecter automatiquement l'URL de base
const getBaseURL = () => {
  // Utiliser l'URL actuelle du navigateur
  const protocol = window.location.protocol; // http: ou https:
  const hostname = window.location.hostname; // IP ou nom de domaine
  const port = window.location.port; // Port (3000 par défaut)
  
  let baseURL;
  
  if (port) {
    baseURL = `${protocol}//${hostname}:${port}`;
  } else {
    baseURL = `${protocol}//${hostname}`;
  }
  
  console.log(`🌐 URL de base détectée: ${baseURL}`);
  return baseURL;
};

// Configuration globale
window.APP_CONFIG = {
  BASE_URL: getBaseURL(),
  API_URL: getBaseURL() + '/api',
  
  // Fonction utilitaire pour construire les URLs d'API
  getApiUrl: (endpoint) => {
    const url = window.APP_CONFIG.API_URL + endpoint;
    console.log(`📡 URL API: ${url}`);
    return url;
  },
  
  // Fonction pour faire des requêtes avec la bonne URL
  fetch: async (endpoint, options = {}) => {
    const url = window.APP_CONFIG.getApiUrl(endpoint);
    
    // Ajouter les headers par défaut
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };
    
    console.log(`🔄 Requête vers: ${url}`, defaultOptions);
    
    try {
      const response = await fetch(url, defaultOptions);
      console.log(`📡 Réponse: ${response.status} ${response.statusText}`);
      return response;
    } catch (error) {
      console.error(`❌ Erreur de connexion vers ${url}:`, error);
      throw new Error(`Impossible de se connecter au serveur (${url}). Vérifiez que le serveur est démarré et accessible.`);
    }
  }
};

// Fonction de test de connectivité
window.testConnection = async () => {
  try {
    console.log('🧪 Test de connectivité...');
    const response = await window.APP_CONFIG.fetch('/products');
    
    if (response.ok) {
      console.log('✅ Connexion au serveur réussie !');
      return true;
    } else {
      console.error('❌ Erreur de connexion:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Test de connexion échoué:', error);
    return false;
  }
};

// Afficher les informations de connexion
console.log(`🌐 Configuration réseau:`);
console.log(`   - URL de base: ${window.APP_CONFIG.BASE_URL}`);
console.log(`   - URL API: ${window.APP_CONFIG.API_URL}`);
console.log(`   - Hostname: ${window.location.hostname}`);
console.log(`   - Port: ${window.location.port || 'défaut'}`);

// Test automatique de connectivité au chargement
setTimeout(() => {
  window.testConnection().then(success => {
    if (success) {
      console.log('🎉 Serveur accessible !');
    } else {
      console.warn('⚠️ Problème de connexion au serveur');
    }
  });
}, 1000);
