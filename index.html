<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GestiCom - Gestion Commerciale</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Custom CSS -->
  <link href="css/style.css" rel="stylesheet">
  <!-- Theme CSS -->
  <link href="css/themes.css" rel="stylesheet">

  <!-- Script pour appliquer le mode sombre immédiatement -->
  <script>
    // Appliquer le mode sombre par défaut immédiatement pour éviter le flash
    (function() {
      console.log('🔍 Initialisation du thème...');

      try {
        const savedTheme = localStorage.getItem('theme');
        console.log('📦 Thème sauvegardé:', savedTheme);

        // Définir le mode sombre par défaut si aucun thème n'est sauvegardé
        if (!savedTheme) {
          localStorage.setItem('theme', 'dark');
          document.documentElement.setAttribute('data-theme', 'dark');
          console.log('🌙 Mode sombre défini par défaut (première visite)');
        } else if (savedTheme === 'dark') {
          document.documentElement.setAttribute('data-theme', 'dark');
          console.log('🌙 Mode sombre appliqué depuis localStorage');
        } else {
          document.documentElement.removeAttribute('data-theme');
          console.log('☀️ Mode clair appliqué depuis localStorage');
        }

        // Vérification finale
        const finalTheme = document.documentElement.getAttribute('data-theme');
        console.log('✅ Attribut data-theme final:', finalTheme);

      } catch (error) {
        console.error('❌ Erreur lors de l\'initialisation du thème:', error);
        // En cas d'erreur, forcer quand même le mode sombre
        document.documentElement.setAttribute('data-theme', 'dark');
      }
    })();
  </script>
</head>
<body>
  <!-- Container pour les notifications -->
  <div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>

  <!-- Barre de navigation supérieure -->
  <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
    <div class="container-fluid">
      <a class="navbar-brand fs-3 fw-bold" href="#">
        <i class="bi bi-shop me-2 fs-2"></i>GestiCom
      </a>

      <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="ms-auto d-flex align-items-center">
        <span class="navbar-text me-3">
          <i class="bi bi-person-circle me-1"></i>
          <span id="username-display">Utilisateur</span>
        </span>

        <button id="navbarThemeToggle" class="btn btn-outline-secondary btn-sm me-2" title="Basculer le thème">
          <i class="bi bi-moon-fill"></i>
        </button>

        <button id="navbarLogout" class="btn btn-outline-danger btn-sm" title="Déconnexion">
          <i class="bi bi-box-arrow-right me-1"></i>
          <span class="d-none d-sm-inline">Déconnexion</span>
        </button>
      </div>
    </div>
  </nav>

  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
        <div class="position-sticky pt-3">
          <ul class="nav flex-column">
            <!-- Tableau de bord -->
            <li class="nav-item">
              <a class="nav-link" href="#" data-section="dashboard">
                <i class="bi bi-speedometer2 me-2"></i>Tableau de bord
              </a>
            </li>

            <!-- Caisse -->
            <li class="nav-item">
              <a class="nav-link" href="#" data-section="pos">
                <i class="bi bi-calculator me-2 text-success"></i>Caisse
              </a>
            </li>

            <!-- Séparateur -->
            <li class="nav-item">
              <hr class="my-2">
            </li>

            <!-- ACHATS -->
            <li class="nav-item">
              <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-3 mb-1 text-muted text-uppercase">
                <span><i class="bi bi-cart-plus me-2"></i>Achats</span>
              </h6>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="purchase-orders">
                <i class="bi bi-cart me-2"></i>Bons de commande
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="purchase-receipts">
                <i class="bi bi-clipboard-check me-2"></i>Bons de réception
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="purchase-invoices">
                <i class="bi bi-receipt-cutoff me-2"></i>Factures d'achat
              </a>
            </li>

            <!-- VENTES -->
            <li class="nav-item">
              <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-3 mb-1 text-muted text-uppercase">
                <span><i class="bi bi-bag-check me-2"></i>Ventes</span>
              </h6>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="quotes">
                <i class="bi bi-file-earmark-text me-2"></i>Devis
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="delivery">
                <i class="bi bi-truck me-2"></i>Bons de livraison
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="invoices">
                <i class="bi bi-receipt me-2"></i>Factures de vente
              </a>
            </li>

            <!-- Séparateur -->
            <li class="nav-item">
              <hr class="my-2">
            </li>

            <!-- GESTION -->
            <li class="nav-item">
              <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-3 mb-1 text-muted text-uppercase">
                <span><i class="bi bi-gear me-2"></i>Gestion</span>
              </h6>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="products">
                <i class="bi bi-box me-2"></i>Produits
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="clients">
                <i class="bi bi-people me-2"></i>Clients
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="suppliers">
                <i class="bi bi-truck me-2"></i>Fournisseurs
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="inventory">
                <i class="bi bi-clipboard-check me-2"></i>Inventaire
              </a>
            </li>

            <!-- RETOURS -->
            <li class="nav-item">
              <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-3 mb-1 text-muted text-uppercase">
                <span><i class="bi bi-arrow-repeat me-2"></i>Retours</span>
              </h6>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="sales-returns">
                <i class="bi bi-arrow-return-left me-2"></i>Retours clients
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link ps-4" href="#" data-section="purchase-returns">
                <i class="bi bi-arrow-return-right me-2"></i>Retours fournisseurs
              </a>
            </li>

            <!-- Séparateur -->
            <li class="nav-item">
              <hr class="my-2">
            </li>

            <!-- ADMINISTRATION -->
            <li class="nav-item">
              <a class="nav-link" href="#" data-section="settings">
                <i class="bi bi-gear me-2"></i>Paramètres
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link text-danger" href="#" data-section="super-admin">
                <i class="bi bi-shield-exclamation me-2"></i>Super Admin
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Main content -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 id="section-title">Tableau de bord</h1>
        </div>

        <!-- Container for dynamic content -->
        <div id="app-container">
          <!-- Content will be loaded here -->
        </div>
      </main>
    </div>
  </div>

  <!-- Conteneur pour les notifications -->
  <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Scripts -->
  <script src="js/config.js"></script>
  <script src="js/theme.js"></script>
  <script src="js/notifications.js"></script>
  <script src="js/auth.js"></script>

  <!-- Vérification d'authentification immédiate -->
  <script>
    // Vérification immédiate avant le chargement complet
    (function() {
      console.log('🔒 Vérification d\'authentification immédiate...');
      const token = localStorage.getItem('authToken');
      const isLoginPage = window.location.href.includes('login.html');

      if (!token && !isLoginPage) {
        console.log('❌ Redirection immédiate vers login.html');
        window.location.replace('login.html');
      }
    })();
  </script>
  <script src="js/clients.js"></script>
  <script src="js/products.js"></script>
  <script src="js/suppliers.js"></script>
  <script src="js/inventory.js"></script>
  <script src="js/pos.js"></script>
  <script src="js/quotes.js"></script>
  <script src="js/invoices.js"></script>
  <script src="js/purchases.js"></script>
  <script src="js/purchase-orders.js"></script>
  <script src="js/purchase-receipts.js"></script>
  <script src="js/purchase-invoices.js"></script>
  <script src="js/sales.js"></script>
  <script src="js/sales-returns.js"></script>
  <script src="js/purchase-returns.js"></script>
  <script src="js/delivery.js"></script>
  <script src="js/settings.js"></script>
  <script src="js/super-admin.js"></script>
  <script src="js/app.js"></script>
</body>
</html>




