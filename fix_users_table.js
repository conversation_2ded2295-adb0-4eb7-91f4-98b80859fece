// Script pour corriger la table des utilisateurs
const mysql = require('mysql2/promise');

async function fixUsersTable() {
  let connection;
  
  try {
    console.log('🔧 Correction de la table des utilisateurs...');
    
    // Connexion à la base de données
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom'
    });

    console.log('✅ Connexion à la base de données réussie');

    // Vérifier si la table existe
    const [tables] = await connection.execute("SHOW TABLES LIKE 'users'");
    
    if (tables.length === 0) {
      console.log('📝 Création de la table users...');
      
      // Créer la table complète
      await connection.execute(`
        CREATE TABLE users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          email VARCHAR(100) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          role ENUM('superadmin', 'admin', 'manager', 'cashier', 'viewer') DEFAULT 'viewer',
          full_name VARCHAR(100),
          phone VARCHAR(20),
          status ENUM('active', 'inactive') DEFAULT 'active',
          last_login TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      
      console.log('✅ Table users créée');
    } else {
      console.log('📋 Table users existe, vérification de la structure...');
      
      // Vérifier les colonnes existantes
      const [columns] = await connection.execute("DESCRIBE users");
      const columnNames = columns.map(col => col.Field);
      
      console.log('Colonnes existantes:', columnNames);
      
      // Ajouter les colonnes manquantes
      const requiredColumns = [
        { name: 'password_hash', definition: 'VARCHAR(255) NOT NULL' },
        { name: 'role', definition: 'ENUM("superadmin", "admin", "manager", "cashier", "viewer") DEFAULT "viewer"' },
        { name: 'full_name', definition: 'VARCHAR(100)' },
        { name: 'phone', definition: 'VARCHAR(20)' },
        { name: 'status', definition: 'ENUM("active", "inactive") DEFAULT "active"' },
        { name: 'last_login', definition: 'TIMESTAMP NULL' },
        { name: 'created_at', definition: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP' },
        { name: 'updated_at', definition: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP' }
      ];
      
      for (const column of requiredColumns) {
        if (!columnNames.includes(column.name)) {
          console.log(`➕ Ajout de la colonne ${column.name}...`);
          await connection.execute(`ALTER TABLE users ADD COLUMN ${column.name} ${column.definition}`);
        }
      }
    }

    // Insérer des utilisateurs de test
    console.log('👥 Insertion des utilisateurs de test...');
    
    const testUsers = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password_hash: 'admin123',
        role: 'superadmin',
        full_name: 'Super Administrateur',
        status: 'active'
      },
      {
        username: 'manager1',
        email: '<EMAIL>',
        password_hash: 'manager123',
        role: 'manager',
        full_name: 'Gestionnaire Commercial',
        status: 'active'
      },
      {
        username: 'cashier1',
        email: '<EMAIL>',
        password_hash: 'caisse123',
        role: 'cashier',
        full_name: 'Caissier Principal',
        status: 'active'
      }
    ];

    for (const user of testUsers) {
      const [existing] = await connection.execute(
        'SELECT id FROM users WHERE username = ?',
        [user.username]
      );

      if (existing.length === 0) {
        await connection.execute(`
          INSERT INTO users (username, email, password_hash, role, full_name, status)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          user.username,
          user.email,
          user.password_hash,
          user.role,
          user.full_name,
          user.status
        ]);

        console.log(`✅ Utilisateur ${user.username} créé (${user.role})`);
      } else {
        console.log(`ℹ️  Utilisateur ${user.username} existe déjà`);
      }
    }

    // Vérifier le résultat final
    const [users] = await connection.execute('SELECT id, username, email, role, full_name, status FROM users');
    
    console.log('\n📊 Utilisateurs dans la base:');
    users.forEach(user => {
      console.log(`   - ${user.username} (${user.role}) - ${user.status}`);
    });

    console.log(`\n✅ Total: ${users.length} utilisateurs`);

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  fixUsersTable().then(() => {
    console.log('\n🎉 Table des utilisateurs corrigée avec succès !');
  }).catch(console.error);
}

module.exports = { fixUsersTable };
