// Script pour créer la table des utilisateurs
const mysql = require('mysql2/promise');

async function createUsersTable() {
  let connection;
  
  try {
    console.log('🔧 Création de la table des utilisateurs...');
    
    // Connexion à la base de données
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'gesticom'
    });

    console.log('✅ Connexion à la base de données réussie');

    // Créer la table des utilisateurs
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role ENUM('superadmin', 'admin', 'manager', 'cashier', 'viewer') DEFAULT 'viewer',
        full_name VARCHAR(100),
        phone VARCHAR(20),
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ Table users créée/vérifiée');

    // Insérer un utilisateur admin par défaut
    const [existingAdmin] = await connection.execute(
      'SELECT id FROM users WHERE username = "admin"'
    );

    if (existingAdmin.length === 0) {
      await connection.execute(`
        INSERT INTO users (username, email, password_hash, role, full_name, status)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        'admin',
        '<EMAIL>',
        'admin123', // Mot de passe simple pour les tests
        'superadmin',
        'Super Administrateur',
        'active'
      ]);

      console.log('✅ Utilisateur admin créé');
      console.log('   - Nom d\'utilisateur: admin');
      console.log('   - Mot de passe: admin123');
    } else {
      console.log('ℹ️  Utilisateur admin déjà existant');
    }

    // Insérer quelques utilisateurs de test
    const testUsers = [
      {
        username: 'manager1',
        email: '<EMAIL>',
        password: 'manager123',
        role: 'manager',
        fullName: 'Gestionnaire Commercial'
      },
      {
        username: 'cashier1',
        email: '<EMAIL>',
        password: 'caisse123',
        role: 'cashier',
        fullName: 'Caissier Principal'
      }
    ];

    for (const user of testUsers) {
      const [existing] = await connection.execute(
        'SELECT id FROM users WHERE username = ?',
        [user.username]
      );

      if (existing.length === 0) {
        await connection.execute(`
          INSERT INTO users (username, email, password_hash, role, full_name, status)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          user.username,
          user.email,
          user.password,
          user.role,
          user.fullName,
          'active'
        ]);

        console.log(`✅ Utilisateur ${user.username} créé (${user.role})`);
      }
    }

    // Afficher le résumé
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    console.log(`\n📊 Total: ${userCount[0].count} utilisateurs dans la base`);

    console.log('\n🚀 Vous pouvez maintenant:');
    console.log('   1. Accéder à la section Super Admin');
    console.log('   2. Gérer les utilisateurs');
    console.log('   3. Configurer le réseau');

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  createUsersTable().then(() => {
    console.log('\n✅ Table des utilisateurs créée avec succès !');
  }).catch(console.error);
}

module.exports = { createUsersTable };
