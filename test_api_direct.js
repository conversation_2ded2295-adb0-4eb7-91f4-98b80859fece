// Test direct de l'API de création d'utilisateur
const fetch = require('node-fetch');

async function testCreateUser() {
  try {
    console.log('🧪 Test de création d\'utilisateur...');
    
    const userData = {
      username: 'azed',
      email: '<EMAIL>',
      password: 'azed123',
      role: 'manager',
      fullName: 'Azed Gestionnaire',
      phone: '0123456789',
      status: 'active'
    };
    
    console.log('📝 Données à envoyer:', userData);
    
    const response = await fetch('http://localhost:3000/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });
    
    console.log('📡 Statut de la réponse:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Succès!');
      console.log('   ID:', result.id);
      console.log('   Username:', result.username);
      console.log('   Message:', result.message);
    } else {
      const errorText = await response.text();
      console.log('❌ Erreur HTTP:', response.status);
      console.log('   Réponse:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Erreur réseau:', error.message);
  }
}

testCreateUser();
