/* Styles personnalisés pour GestiCom */

/* Sidebar */
.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 48px 0 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
  border-radius: 0.375rem;
  margin: 0.125rem 0.5rem;
}

.sidebar .nav-link:hover {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

/* Main content */
main {
  margin-left: 240px;
}

@media (max-width: 767.98px) {
  main {
    margin-left: 0;
  }
}

/* Styles pour les cartes de statistiques */
.stat-card {
  border-left: 4px solid #007bff;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.success {
  border-left-color: #28a745;
}

.stat-card.warning {
  border-left-color: #ffc107;
}

.stat-card.danger {
  border-left-color: #dc3545;
}

/* Styles pour les notifications */
#notification-container {
  max-height: 100vh;
  overflow-y: auto;
}

#notification-container .alert {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
  border-radius: 8px;
  animation: slideInRight 0.3s ease-out;
}

#notification-container .alert-success {
  background-color: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

#notification-container .alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

#notification-container .alert-warning {
  background-color: #fff3cd;
  color: #856404;
  border-left: 4px solid #ffc107;
}

#notification-container .alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Styles pour les tableaux */
.table th {
  border-top: none;
  font-weight: 600;
  background-color: #f8f9fa;
}

/* Styles pour les boutons d'action */
.btn-sm {
  margin: 0 2px;
}

/* Styles pour les modals */
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  font-weight: 600;
}

/* Styles pour les formulaires */
.form-label {
  font-weight: 500;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Styles pour les badges de statut */
.badge {
  font-size: 0.75em;
}

/* Styles pour les cartes */
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Styles pour les alertes personnalisées */
.alert {
  border: none;
  border-radius: 0.5rem;
}

/* Styles pour les icônes */
.bi {
  vertical-align: -0.125em;
}

/* Styles pour les liens de navigation */
.nav-link {
  transition: all 0.2s ease-in-out;
}

/* Styles pour les boutons */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Styles pour les inputs de recherche */
.input-group .form-control {
  border-right: none;
}

.input-group .btn {
  border-left: none;
  background-color: #f8f9fa;
  border-color: #ced4da;
}

/* Styles responsifs */
@media (max-width: 576px) {
  .btn-toolbar .btn-group {
    margin-bottom: 0.5rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

/* Styles pour les états de chargement */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Styles pour les éléments désactivés */
.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* Styles pour les tooltips personnalisés */
.tooltip {
  font-size: 0.875rem;
}

/* Styles pour les dropdowns */
.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  border-radius: 0.5rem;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

/* Styles pour les progress bars */
.progress {
  height: 0.5rem;
  border-radius: 0.25rem;
}

/* Styles pour les listes */
.list-group-item {
  border: none;
  border-bottom: 1px solid #dee2e6;
}

.list-group-item:last-child {
  border-bottom: none;
}

/* Styles pour les tabs */
.nav-tabs .nav-link {
  border: none;
  border-bottom: 2px solid transparent;
  background: none;
}

.nav-tabs .nav-link.active {
  border-bottom-color: #007bff;
  background: none;
}

/* Styles pour les spinners */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Styles pour les breadcrumbs */
.breadcrumb {
  background: none;
  padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #6c757d;
}

/* Styles pour les accordions */
.accordion-button {
  background-color: #f8f9fa;
  border: none;
}

.accordion-button:not(.collapsed) {
  background-color: #e9ecef;
  color: #007bff;
}

/* Styles pour les offcanvas */
.offcanvas {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

/* Styles pour la navigation organisée */
.sidebar-heading {
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.nav-link.ps-4 {
  padding-left: 1.5rem !important;
  font-size: 0.9rem;
  position: relative;
}

.nav-link.ps-4::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background-color: #6c757d;
  border-radius: 50%;
  opacity: 0.5;
}

.nav-link.ps-4:hover::before {
  background-color: #007bff;
  opacity: 1;
}

.sidebar hr {
  margin: 0.5rem 1rem;
  opacity: 0.3;
  border-color: #dee2e6;
}

/* Amélioration des sections */
.sidebar .nav-item h6 {
  color: #6c757d;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.sidebar .nav-item h6:first-child {
  margin-top: 0.5rem;
}

/* Styles pour les icônes des sections */
.sidebar .nav-item h6 i {
  font-size: 0.875rem;
  margin-right: 0.5rem;
}

/* Hover effects pour les sous-sections */
.nav-link.ps-4:hover {
  background-color: rgba(0, 123, 255, 0.08);
  border-radius: 0.25rem;
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

/* Active state pour les sous-sections */
.nav-link.ps-4.active {
  background-color: rgba(0, 123, 255, 0.15);
  color: #007bff;
  font-weight: 600;
}

/* Styles pour les menus déroulants */
.nav-link[data-bs-toggle="collapse"] {
  font-weight: 600;
  color: #495057;
  transition: all 0.3s ease;
}

.nav-link[data-bs-toggle="collapse"]:hover {
  background-color: rgba(0, 123, 255, 0.08);
  color: #007bff;
}

.nav-link[data-bs-toggle="collapse"] .bi-chevron-down {
  transition: transform 0.3s ease;
  font-size: 0.8rem;
}

.nav-link[data-bs-toggle="collapse"][aria-expanded="true"] .bi-chevron-down {
  transform: rotate(180deg);
}

.nav-link[data-bs-toggle="collapse"][aria-expanded="true"] {
  background-color: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

/* Styles pour les sous-menus */
.collapse .nav {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.collapse .nav-link {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  color: #6c757d;
  border-radius: 0.25rem;
  margin: 0.125rem 0;
  transition: all 0.2s ease;
}

.collapse .nav-link:hover {
  background-color: rgba(0, 123, 255, 0.08);
  color: #007bff;
  transform: translateX(4px);
}

.collapse .nav-link.active {
  background-color: rgba(0, 123, 255, 0.15);
  color: #007bff;
  font-weight: 600;
  border-left: 3px solid #007bff;
  padding-left: 0.625rem;
}

/* Animation pour l'ouverture/fermeture */
.collapse {
  transition: height 0.35s ease;
}

.collapsing {
  transition: height 0.35s ease;
}

/* Styles spécifiques pour les sections Achats et Ventes */
.nav-item:has([data-bs-target="#purchasesCollapse"]) .nav-link {
  border-left: 4px solid transparent;
}

.nav-item:has([data-bs-target="#purchasesCollapse"]) .nav-link:hover,
.nav-item:has([data-bs-target="#purchasesCollapse"]) .nav-link[aria-expanded="true"] {
  border-left-color: #dc3545;
}

.nav-item:has([data-bs-target="#salesCollapse"]) .nav-link {
  border-left: 4px solid transparent;
}

.nav-item:has([data-bs-target="#salesCollapse"]) .nav-link:hover,
.nav-item:has([data-bs-target="#salesCollapse"]) .nav-link[aria-expanded="true"] {
  border-left-color: #28a745;
}

/* Icônes colorées pour les sections */
#purchasesCollapse .nav-link i {
  color: #dc3545;
}

#salesCollapse .nav-link i {
  color: #28a745;
}

/* Styles spécifiques pour la section Retours */
.nav-item:has([data-bs-target="#returnsCollapse"]) .nav-link {
  border-left: 4px solid transparent;
}

.nav-item:has([data-bs-target="#returnsCollapse"]) .nav-link:hover,
.nav-item:has([data-bs-target="#returnsCollapse"]) .nav-link[aria-expanded="true"] {
  border-left-color: #fd7e14;
}

/* Icônes colorées pour la section Retours */
#returnsCollapse .nav-link i {
  color: #fd7e14;
}
