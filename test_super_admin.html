<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Super Admin</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
  <div class="container mt-5">
    <h1>Test Super Admin</h1>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Test API Utilisateurs</h5>
          </div>
          <div class="card-body">
            <button id="testGetUsers" class="btn btn-primary mb-2">Tester GET /api/users</button>
            <button id="testCreateUser" class="btn btn-success mb-2">Tester Création Utilisateur</button>
            <div id="apiResults" class="mt-3"></div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Test Formulaire</h5>
          </div>
          <div class="card-body">
            <form id="testForm">
              <div class="mb-3">
                <label for="testUsername" class="form-label">Nom d'utilisateur</label>
                <input type="text" class="form-control" id="testUsername" value="testuser" required>
              </div>
              <div class="mb-3">
                <label for="testEmail" class="form-label">Email</label>
                <input type="email" class="form-control" id="testEmail" value="<EMAIL>" required>
              </div>
              <div class="mb-3">
                <label for="testPassword" class="form-label">Mot de passe</label>
                <input type="password" class="form-control" id="testPassword" value="test123" required>
              </div>
              <button type="button" id="testSaveBtn" class="btn btn-primary">Test Sauvegarde</button>
            </form>
            <div id="formResults" class="mt-3"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Test de l'API
    document.getElementById('testGetUsers').addEventListener('click', async () => {
      const results = document.getElementById('apiResults');
      results.innerHTML = '<div class="text-info">Chargement...</div>';
      
      try {
        const response = await fetch('/api/users');
        if (response.ok) {
          const users = await response.json();
          results.innerHTML = `
            <div class="alert alert-success">
              <strong>✅ Succès!</strong><br>
              ${users.length} utilisateurs récupérés:<br>
              ${users.map(u => `- ${u.username} (${u.role})`).join('<br>')}
            </div>
          `;
        } else {
          results.innerHTML = `<div class="alert alert-danger">❌ Erreur HTTP: ${response.status}</div>`;
        }
      } catch (error) {
        results.innerHTML = `<div class="alert alert-danger">❌ Erreur: ${error.message}</div>`;
      }
    });

    // Test de création d'utilisateur
    document.getElementById('testCreateUser').addEventListener('click', async () => {
      const results = document.getElementById('apiResults');
      results.innerHTML = '<div class="text-info">Création en cours...</div>';
      
      const userData = {
        username: 'testuser_' + Date.now(),
        email: 'test_' + Date.now() + '@example.com',
        password: 'test123',
        role: 'viewer',
        fullName: 'Utilisateur Test',
        status: 'active'
      };
      
      try {
        const response = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });
        
        if (response.ok) {
          const result = await response.json();
          results.innerHTML = `
            <div class="alert alert-success">
              <strong>✅ Utilisateur créé!</strong><br>
              ID: ${result.id}<br>
              Message: ${result.message}
            </div>
          `;
        } else {
          const errorData = await response.json();
          results.innerHTML = `<div class="alert alert-danger">❌ Erreur: ${errorData.error}</div>`;
        }
      } catch (error) {
        results.innerHTML = `<div class="alert alert-danger">❌ Erreur: ${error.message}</div>`;
      }
    });

    // Test du formulaire
    document.getElementById('testSaveBtn').addEventListener('click', async () => {
      const results = document.getElementById('formResults');
      results.innerHTML = '<div class="text-info">Test en cours...</div>';
      
      const form = document.getElementById('testForm');
      if (!form.checkValidity()) {
        results.innerHTML = '<div class="alert alert-warning">❌ Formulaire invalide</div>';
        form.reportValidity();
        return;
      }
      
      const userData = {
        username: document.getElementById('testUsername').value,
        email: document.getElementById('testEmail').value,
        password: document.getElementById('testPassword').value,
        role: 'viewer',
        fullName: 'Test User',
        status: 'active'
      };
      
      try {
        const response = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });
        
        if (response.ok) {
          const result = await response.json();
          results.innerHTML = `
            <div class="alert alert-success">
              <strong>✅ Formulaire fonctionne!</strong><br>
              Utilisateur créé: ${result.username}<br>
              ID: ${result.id}
            </div>
          `;
        } else {
          const errorData = await response.json();
          results.innerHTML = `<div class="alert alert-danger">❌ Erreur API: ${errorData.error}</div>`;
        }
      } catch (error) {
        results.innerHTML = `<div class="alert alert-danger">❌ Erreur réseau: ${error.message}</div>`;
      }
    });

    // Test au chargement de la page
    window.addEventListener('load', () => {
      console.log('🧪 Page de test chargée');
      console.log('🔍 Test de l\'API au chargement...');
      document.getElementById('testGetUsers').click();
    });
  </script>
</body>
</html>
