<div class="container">
  <div class="row mb-3">
    <div class="col">
      <div class="d-flex justify-content-between align-items-center">
        <h3>Factures</h3>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addInvoiceModal">
          <i class="bi bi-plus-circle me-2"></i>Nouvelle facture
        </button>
      </div>
    </div>
  </div>

  <div class="row mb-3">
    <div class="col">
      <div class="input-group">
        <span class="input-group-text"><i class="bi bi-search"></i></span>
        <input type="text" class="form-control" id="searchInvoice" placeholder="Rechercher une facture...">
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead>
        <tr>
          <th>N° Facture</th>
          <th>Date</th>
          <th>Client</th>
          <th>Montant</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody id="invoicesTableBody">
        <!-- Les données seront chargées dynamiquement -->
      </tbody>
    </table>
  </div>
</div>

<!-- Modal pour ajouter une facture -->
<div class="modal fade" id="addInvoiceModal" tabindex="-1" aria-labelledby="addInvoiceModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addInvoiceModalLabel">Nouvelle facture</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="addInvoiceForm">
          <input type="hidden" id="invoiceId">
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="invoiceDate" class="form-label">Date</label>
              <input type="date" class="form-control" id="invoiceDate" required>
            </div>
            <div class="col-md-6">
              <label for="invoiceClient" class="form-label">Client</label>
              <select class="form-select" id="invoiceClient" required>
                <option value="">Sélectionner un client</option>
                <!-- Options chargées dynamiquement -->
              </select>
            </div>
          </div>

          <div class="mb-3">
            <label for="invoiceDelivery" class="form-label">Bon de livraison associé</label>
            <select class="form-select" id="invoiceDelivery">
              <option value="">Sélectionner un bon de livraison (optionnel)</option>
              <!-- Options chargées dynamiquement -->
            </select>
          </div>

          <h6 class="mt-4 mb-3">Produits</h6>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Produit</th>
                  <th>Quantité</th>
                  <th>Prix unitaire</th>
                  <th>Total</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="invoiceItemsTableBody">
                <!-- Les lignes de produits seront ajoutées dynamiquement -->
              </tbody>
            </table>
          </div>

          <div class="mb-3 row">
            <label class="col-sm-4 col-form-label">Sous-total HT</label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="invoiceSubtotal" readonly>
            </div>
          </div>

          <div class="mb-3 row">
            <label class="col-sm-4 col-form-label">TVA (19%)</label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="invoiceTax" readonly>
            </div>
          </div>

          <div class="mb-3 row">
            <label class="col-sm-4 col-form-label">Total TTC</label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="invoiceTotal" readonly>
            </div>
          </div>

          <div class="mb-3">
            <button type="button" class="btn btn-secondary" id="addInvoiceItem">Ajouter un produit</button>
          </div>

          <div class="mb-3">
            <label for="invoiceStatus" class="form-label">Statut</label>
            <select class="form-select" id="invoiceStatus" required>
              <option value="En attente">En attente</option>
              <option value="Payée">Payée</option>
              <option value="Annulée">Annulée</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="invoicePaymentMethod" class="form-label">Méthode de paiement</label>
            <select class="form-select" id="invoicePaymentMethod" required>
              <option value="Espèces">Espèces</option>
              <option value="Chèque">Chèque</option>
              <option value="Carte bancaire">Carte bancaire</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="invoicePaymentDate" class="form-label">Date de paiement</label>
            <input type="date" class="form-control" id="invoicePaymentDate">
          </div>

          <div class="mb-3">
            <label for="invoiceNote" class="form-label">Note</label>
            <textarea class="form-control" id="invoiceNote" rows="3"></textarea>
          </div>

          <div class="mb-3">
            <button type="button" class="btn btn-primary" id="saveInvoiceBtn">Enregistrer la facture</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour l'aperçu de la facture -->
<div class="modal fade" id="viewInvoiceModal" tabindex="-1" aria-labelledby="viewInvoiceModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="viewInvoiceModalLabel">Aperçu de la facture</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="invoicePreviewContent">
          <!-- Le contenu de l'aperçu sera généré dynamiquement -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        <button type="button" class="btn btn-primary" id="editFromPreview">Modifier</button>

      </div>
    </div>
  </div>
</div>
